import json
import base64
from pathlib import Path
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Optional

class PasswordManagerService:
    """密码安全存储管理服务"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".mysuite"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.key_file = self.config_dir / ".key"
        self.password_file = self.config_dir / "saved_passwords.encrypted"
        self._ensure_key()
    
    def _ensure_key(self):
        """确保加密密钥存在"""
        if not self.key_file.exists():
            # 生成新的加密密钥
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件权限（仅用户可读）
            try:
                self.key_file.chmod(0o600)
            except:
                pass  # Windows可能不支持chmod
    
    def _get_cipher(self):
        """获取加密器"""
        with open(self.key_file, 'rb') as f:
            key = f.read()
        return <PERSON><PERSON>t(key)
    
    def save_password(self, employee_id: str, password: str) -> bool:
        """保存加密的密码"""
        try:
            cipher = self._get_cipher()
            
            # 加载现有密码数据
            passwords = {}
            if self.password_file.exists():
                try:
                    with open(self.password_file, 'rb') as f:
                        encrypted_data = f.read()
                    if encrypted_data:
                        decrypted_data = cipher.decrypt(encrypted_data)
                        passwords = json.loads(decrypted_data.decode())
                except:
                    passwords = {}  # 如果解密失败，重新开始
            
            # 保存新密码
            passwords[employee_id] = password
            
            # 加密并保存
            data_to_encrypt = json.dumps(passwords).encode()
            encrypted_data = cipher.encrypt(data_to_encrypt)
            
            with open(self.password_file, 'wb') as f:
                f.write(encrypted_data)
            
            # 设置文件权限
            try:
                self.password_file.chmod(0o600)
            except:
                pass
            
            print(f"Password saved for employee: {employee_id}")
            return True
        except Exception as e:
            print(f"Failed to save password: {e}")
            return False
    
    def load_password(self, employee_id: str) -> str:
        """加载解密的密码"""
        try:
            if not self.password_file.exists():
                return ""
            
            cipher = self._get_cipher()
            
            with open(self.password_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return ""
            
            decrypted_data = cipher.decrypt(encrypted_data)
            passwords = json.loads(decrypted_data.decode())
            
            return passwords.get(employee_id, "")
        except Exception as e:
            print(f"Failed to load password: {e}")
            return ""
    
    def remove_password(self, employee_id: str) -> bool:
        """删除保存的密码"""
        try:
            if not self.password_file.exists():
                return True
            
            cipher = self._get_cipher()
            
            with open(self.password_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return True
            
            decrypted_data = cipher.decrypt(encrypted_data)
            passwords = json.loads(decrypted_data.decode())
            
            if employee_id in passwords:
                del passwords[employee_id]
                
                # 重新加密并保存
                data_to_encrypt = json.dumps(passwords).encode()
                encrypted_data = cipher.encrypt(data_to_encrypt)
                
                with open(self.password_file, 'wb') as f:
                    f.write(encrypted_data)
                
                print(f"Password removed for employee: {employee_id}")
            
            return True
        except Exception as e:
            print(f"Failed to remove password: {e}")
            return False

class ConfigService:
    """配置管理服务"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".mysuite"
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def load_local_flags(self) -> dict:
        """加载本地配置"""
        config_path = self.config_dir / "flags.json"
        try:
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Loading local flags failed: {e}")
        return {}
    
    def save_local_flags(self, flags: dict) -> bool:
        """保存本地配置"""
        config_path = self.config_dir / "flags.json"
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(flags, f, indent=2, ensure_ascii=False)
            print(f"Saved local flags: {flags}")
            return True
        except Exception as e:
            print(f"Saving local flags failed: {e}")
            return False
    
    def load_last_used_employee_id(self) -> Optional[str]:
        """加载最近使用的员工ID"""
        config_path = self.config_dir / "last_user.json"
        try:
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('last_employee_id', '')
        except Exception as e:
            print(f"Loading last used employee ID failed: {e}")
        return None
    
    def save_last_used_employee_id(self, employee_id: str) -> bool:
        """保存最近使用的员工ID"""
        config_path = self.config_dir / "last_user.json"
        try:
            config = {'last_employee_id': employee_id}
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Saving last used employee ID failed: {e}")
            return False 