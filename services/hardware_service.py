import platform
import subprocess
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from models.hardware import HardwareFingerprintModel

class HardwareFingerprintService:
    """硬件指纹收集服务"""
    
    def __init__(self):
        self.system = platform.system().lower()
    
    def get_mac_address(self) -> str:
        """获取MAC地址"""
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            return mac
        except:
            return "unknown"
    
    def get_motherboard_uuid(self) -> str:
        """获取主板UUID"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'csproduct', 'get', 'UUID'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'UUID':
                        return line
            else:  # Linux
                try:
                    with open('/sys/class/dmi/id/product_uuid', 'r') as f:
                        return f.read().strip()
                except:
                    # 备用方法
                    result = subprocess.run(['dmidecode', '-s', 'system-uuid'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        return result.stdout.strip()
        except:
            pass
        return str(uuid.uuid4())  # 生成随机UUID作为备用
    
    def get_machine_id(self) -> str:
        """获取机器ID"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'computersystem', 'get', 'Name'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'Name':
                        return line
            else:  # Linux
                try:
                    with open('/etc/machine-id', 'r') as f:
                        return f.read().strip()
                except:
                    # 备用方法
                    try:
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            return f.read().strip()
                    except:
                        return platform.node()
        except:
            pass
        return platform.node()  # 使用主机名作为备用
    
    def get_cpu_info(self) -> str:
        """获取CPU信息"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'cpu', 'get', 'Name'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'Name':
                        return line
            else:  # Linux
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if line.startswith('model name'):
                                return line.split(':')[1].strip()
                except:
                    # 备用方法
                    result = subprocess.run(['lscpu'], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'Model name:' in line:
                                return line.split(':')[1].strip()
        except:
            pass
        return "unknown"
    
    def get_disk_serial(self) -> str:
        """获取磁盘序列号"""
        try:
            if self.system == "windows":
                result = subprocess.run(['wmic', 'diskdrive', 'get', 'SerialNumber'], 
                                      capture_output=True, text=True, timeout=10)
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line != 'SerialNumber':
                        return line
            else:  # Linux
                try:
                    result = subprocess.run(['lsblk', '-no', 'SERIAL'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        serials = result.stdout.strip().split('\n')
                        for serial in serials:
                            if serial.strip():
                                return serial.strip()
                except:
                    pass
        except:
            pass
        return "unknown"
    
    def collect_all(self) -> HardwareFingerprintModel:
        """收集所有硬件信息"""
        data = {
            'mac_address': self.get_mac_address(),
            'motherboard_uuid': self.get_motherboard_uuid(),
            'machine_id': self.get_machine_id(),
            'cpu_info': self.get_cpu_info(),
            'disk_serial': self.get_disk_serial(),
            'platform': self.system,
            'timestamp': datetime.now().isoformat()
        }
        return HardwareFingerprintModel.from_dict(data)
    
    def collect_all_dict(self) -> Dict[str, Any]:
        """收集所有硬件信息并返回字典格式"""
        return self.collect_all().to_dict()

class PlatformConfigService:
    """平台配置服务"""
    
    def __init__(self):
        self.system = platform.system().lower()
    
    def get_default_watch_folder(self) -> Path:
        """获取默认监控文件夹"""
        if self.system == "windows":
            return Path.home() / "Documents" / "MySuite" / "watch"
        else:  # Linux
            return Path.home() / ".mysuite" / "watch"
    
    def get_default_serial_port(self) -> str:
        """获取默认串口"""
        if self.system == "windows":
            return "COM1"
        else:  # Linux
            return "/dev/ttyUSB0"
    
    def get_serial_ports(self) -> list:
        """获取可用串口列表"""
        if self.system == "windows":
            return [f"COM{i}" for i in range(1, 21)]
        else:  # Linux
            return [f"/dev/ttyUSB{i}" for i in range(10)] + [f"/dev/ttyACM{i}" for i in range(10)]
    
    def create_directories(self, path: Path):
        """创建目录"""
        path.mkdir(parents=True, exist_ok=True) 