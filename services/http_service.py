import requests
import threading
import asyncio
import time
from typing import Dict, Any, Optional
from PySide6.QtCore import QObject, Signal, QMetaObject, Qt

class SimpleAsyncHTTPClient(QObject):
    """简化的异步HTTP客户端，不依赖aiohttp"""
    
    request_finished = Signal(str, dict, object)
    
    def __init__(self, base_url: str = "https://localhost"):
        super().__init__()
        self.base_url = base_url
        self.session = self._create_session()
        self.loop = None
        self._active_threads = set()  # 跟踪活跃的线程
        self._shutdown_event = threading.Event()
    
    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """设置事件循环"""
        self.loop = loop
    
    def _create_session(self):
        """创建配置好的requests session"""
        session = requests.Session()
        session.trust_env = False
        session.verify = False
        return session
    
    def _make_sync_request(self, method: str, endpoint: str, request_id: str, **kwargs):
        """在后台线程中执行同步请求"""
        try:
            if self._shutdown_event.is_set():
                return
            
            url = f"{self.base_url}{endpoint}"
            
            if method.upper() == 'POST':
                response = self.session.post(url, **kwargs)
            else:
                response = self.session.get(url, **kwargs)
            
            result = {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
            
            if not self._shutdown_event.is_set():
                self._emit_result(request_id, result, None)
                
        except Exception as e:
            if not self._shutdown_event.is_set():
                result = {
                    'status_code': 0,
                    'data': None,
                    'ok': False,
                    'error': str(e)
                }
                self._emit_result(request_id, result, str(e))
        finally:
            # 从活跃线程集合中移除当前线程
            current_thread = threading.current_thread()
            if current_thread in self._active_threads:
                self._active_threads.remove(current_thread)
    
    def _emit_result(self, request_id: str, result: dict, error: str):
        """发送结果信号"""
        if self.loop and self.loop.is_running():
            self.loop.call_soon_threadsafe(
                lambda: self.request_finished.emit(request_id, result, error)
            )
        else:
            self.request_finished.emit(request_id, result, error)
    
    def post_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步POST请求"""
        if request_id is None:
            request_id = f"post_{int(time.time() * 1000)}"
        
        if self._shutdown_event.is_set():
            return request_id
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('POST', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        self._active_threads.add(thread)
        thread.start()
        
        return request_id
    
    def get_async(self, endpoint: str, request_id: str = None, **kwargs) -> str:
        """异步GET请求"""
        if request_id is None:
            request_id = f"get_{int(time.time() * 1000)}"
        
        if self._shutdown_event.is_set():
            return request_id
        
        # 在后台线程中执行请求
        thread = threading.Thread(
            target=self._make_sync_request,
            args=('GET', endpoint, request_id),
            kwargs=kwargs,
            daemon=True
        )
        self._active_threads.add(thread)
        thread.start()
        
        return request_id
    
    async def close(self):
        """关闭客户端"""
        self._shutdown_event.set()
        
        # 等待所有活跃线程完成
        if self._active_threads:
            print(f"Waiting for {len(self._active_threads)} HTTP threads to finish...")
            for thread in list(self._active_threads):
                if thread.is_alive():
                    thread.join(timeout=2.0)
                    if thread.is_alive():
                        print(f"Warning: HTTP thread {thread.name} did not finish within timeout")
        
        if self.session:
            self.session.close()

class SimpleSyncHTTPClient:
    """简化的同步HTTP客户端"""
    
    def __init__(self, base_url: str = "https://localhost"):
        self.base_url = base_url
        self.session = self._create_session()
    
    def _create_session(self):
        """创建配置好的requests session"""
        session = requests.Session()
        session.trust_env = False
        session.verify = False
        return session
    
    def post(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步POST请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.post(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def get(self, endpoint: str, timeout: int = 3, headers: dict = None, **kwargs) -> dict:
        """同步GET请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, timeout=timeout, headers=headers, **kwargs)
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                'ok': response.ok,
                'headers': dict(response.headers)
            }
        except Exception as e:
            return {
                'status_code': 0,
                'data': None,
                'ok': False,
                'error': str(e)
            }
    
    def close(self):
        """关闭session"""
        if self.session:
            self.session.close()

class HTTPService:
    """HTTP服务封装"""
    
    def __init__(self, base_url_main: str = "https://localhost", base_url_auth: str = "http://localhost:8006"):
        self.base_url_main = base_url_main
        self.base_url_auth = base_url_auth
        
        # 主业务服务客户端
        self.async_client_main = SimpleAsyncHTTPClient(base_url_main)
        self.sync_client_main = SimpleSyncHTTPClient(base_url_main)
        
        # 认证服务客户端
        self.async_client_auth = SimpleAsyncHTTPClient(base_url_auth)
        self.sync_client_auth = SimpleSyncHTTPClient(base_url_auth)
    
    def set_event_loop(self, loop: asyncio.AbstractEventLoop):
        """设置事件循环"""
        self.async_client_main.set_event_loop(loop)
        self.async_client_auth.set_event_loop(loop)
    
    def get_async_client_main(self) -> SimpleAsyncHTTPClient:
        """获取主业务服务异步客户端"""
        return self.async_client_main
    
    def get_sync_client_main(self) -> SimpleSyncHTTPClient:
        """获取主业务服务同步客户端"""
        return self.sync_client_main
    
    def get_async_client_auth(self) -> SimpleAsyncHTTPClient:
        """获取认证服务异步客户端"""
        return self.async_client_auth
    
    def get_sync_client_auth(self) -> SimpleSyncHTTPClient:
        """获取认证服务同步客户端"""
        return self.sync_client_auth
    
    # 保持向后兼容
    def get_async_client(self) -> SimpleAsyncHTTPClient:
        """获取异步客户端（默认主业务服务）"""
        return self.async_client_main
    
    def get_sync_client(self) -> SimpleSyncHTTPClient:
        """获取同步客户端（默认主业务服务）"""
        return self.sync_client_main
    
    def close(self):
        """关闭服务"""
        print("Closing HTTP service...")
        
        # 关闭同步客户端
        self.sync_client_main.close()
        self.sync_client_auth.close()
        
        # 关闭异步客户端
        if self.async_client_main.loop and self.async_client_main.loop.is_running():
            try:
                asyncio.run_coroutine_threadsafe(
                    self.async_client_main.close(), 
                    self.async_client_main.loop
                )
            except Exception as e:
                print(f"Error closing async client main: {e}")
        
        if self.async_client_auth.loop and self.async_client_auth.loop.is_running():
            try:
                asyncio.run_coroutine_threadsafe(
                    self.async_client_auth.close(), 
                    self.async_client_auth.loop
                )
            except Exception as e:
                print(f"Error closing async client auth: {e}")
        
        print("HTTP service closed.") 