# client/main_mvvm.py
# MySuite 客户端 - MVVM架构版本

import sys
import json
import threading
import asyncio
import requests
import os
import platform
from pathlib import Path
from datetime import datetime, timedelta
from PySide6 import QtWidgets, QtCore, QtGui
from websockets.client import connect
import urllib3
import uuid
import subprocess
import hashlib
import base64
from cryptography.fernet import Fernet
import mimetypes

# 忽略SSL警告（如开发环境自签名证书）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 导入MVVM组件
from launcher.models.user import UserModel
from launcher.models.hardware import HardwareFingerprintModel
from launcher.models.feature_flags import FeatureFlagsState
from launcher.services.http_service import HTTPService
from launcher.services.hardware_service import HardwareFingerprintService, PlatformConfigService
from launcher.services.password_service import PasswordManagerService, ConfigService
from launcher.viewmodels.auth_viewmodel import AuthViewModel
from launcher.viewmodels.feature_viewmodel import FeatureViewModel
from launcher.views.login_view import LoginView
#from launcher.views.dialogs import AdminPasswordDialog, InputDialog
from launcher.ui.ui_admin_password_dialog import Ui_AdminPasswordDialog
from launcher.ui.ui_input_dialog import Ui_InputDialog

# 服务器配置
SERVER_HTTP_BASE = "https://localhost"
SERVER_WS_URL = "wss://localhost/ws/feature_flags?token="
CLIENT_VERSION = "1.0.0"

class _BaseModule:
    """基础模块类"""
    def __init__(self):
        self._is_running = False
    def start(self):
        self._is_running = True
        print(f"Started {self.__class__.__name__}")
    def stop(self):
        self._is_running = False
        print(f"Stopped {self.__class__.__name__}")
    def is_running(self):
        return self._is_running

class FloatingReminderModule(_BaseModule): 
    pass

class ExcelMonitorModule(_BaseModule):
    def __init__(self, watch_folder): 
        super().__init__()
        self.watch_folder = Path(watch_folder)

class SerialListenerModule(_BaseModule):
    def __init__(self, port, baudrate): 
        super().__init__()
        self.port = port
        self.baudrate = baudrate

class FeatureManager:
    """功能管理器"""
    def __init__(self):
        self.platform_config = PlatformConfigService()
        
        self.modules = {
            "floating_reminder": FloatingReminderModule(),
            "excel_monitor": ExcelMonitorModule(
                watch_folder=self.platform_config.get_default_watch_folder()
            ),
            "serial_listener": SerialListenerModule(
                port=self.platform_config.get_default_serial_port(), 
                baudrate=9600
            ),
        }
        self.current_flags = {name: False for name in self.modules.keys()}

    def apply_flags(self, flags: dict):
        for name, enabled in flags.items():
            module = self.modules.get(name)
            if not module:
                continue
            
            is_running = module.is_running()
            if enabled and not is_running:
                module.start()
            elif not enabled and is_running:
                module.stop()
        
        # Also update our internal state record
        self.current_flags.update(flags)

    def stop_all(self):
        for module in self.modules.values():
            if module.is_running():
                module.stop()

class MainWindow(QtWidgets.QMainWindow):
    """主窗口 - MySuite 客户端主界面（MVVM架构）"""
    
    def __init__(self, client_app):
        super().__init__()
        self.client_app = client_app
        
        # 初始化服务
        self.http_service = HTTPService(base_url_main=SERVER_HTTP_BASE)
        
        # 初始化视图模型
        self.auth_viewmodel = AuthViewModel(self.http_service)
        self.feature_viewmodel = FeatureViewModel(self.http_service)
        
        # 初始化视图
        self.login_view = LoginView()
        
        # 连接信号槽
        self.connect_signals()
        
        # 设置UI
        self.setup_ui()
        
        # 监听窗口状态变化
        self.installEventFilter(self)
        
        # 启动时自动刷新
        QtCore.QTimer.singleShot(1000, self.feature_viewmodel.refresh_server_status)
        QtCore.QTimer.singleShot(500, self.load_last_used_credentials)
    
    def connect_signals(self):
        """连接信号槽"""
        # 登录视图信号连接
        self.login_view.login_requested.connect(self.auth_viewmodel.login)
        self.login_view.register_requested.connect(self.auth_viewmodel.register_hardware_fingerprint)
        self.login_view.delete_registration_requested.connect(self.auth_viewmodel.delete_hardware_registration)
        self.login_view.employee_id_changed.connect(self.on_employee_id_changed)
        self.login_view.login_cancelled.connect(self.on_login_cancel)
        
        # 认证视图模型信号连接
        self.auth_viewmodel.login_successful.connect(self.on_login_successful)
        self.auth_viewmodel.login_failed.connect(self.on_login_failed)
        self.auth_viewmodel.register_successful.connect(self.on_register_successful)
        self.auth_viewmodel.register_failed.connect(self.on_register_failed)
        self.auth_viewmodel.delete_registration_successful.connect(self.on_delete_registration_successful)
        self.auth_viewmodel.delete_registration_failed.connect(self.on_delete_registration_failed)
        self.auth_viewmodel.hardware_verification_failed.connect(self.on_hardware_verification_failed)
        
        # 功能视图模型信号连接
        self.feature_viewmodel.program_launch_successful.connect(self.on_program_launch_successful)
        self.feature_viewmodel.program_launch_failed.connect(self.on_program_launch_failed)
        self.feature_viewmodel.permissions_updated.connect(self.on_permissions_updated)
        self.feature_viewmodel.server_status_updated.connect(self.on_server_status_updated)
        self.feature_viewmodel.feature_flags_updated.connect(self.on_feature_flags_updated)
        
        # HTTP异步请求信号连接
        self.http_service.get_async_client().request_finished.connect(self.on_async_request_finished)
    
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("MySuite クライアント - MVVM版")
        self.setGeometry(100, 100, 600, 400)
        
        # 主体布局
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # 平台信息显示
        platform_info = QtWidgets.QLabel(f"現在のプラットフォーム：{platform.system()} {platform.release()}")
        platform_info.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(platform_info)
        
        # 状态分组
        status_group = QtWidgets.QGroupBox("サーバー状態")
        status_layout = QtWidgets.QVBoxLayout(status_group)
        
        self.server_status_label = QtWidgets.QLabel("サーバー：確認中...")
        self.db_status_label = QtWidgets.QLabel("データベース：確認中...")
        self.websocket_status_label = QtWidgets.QLabel("WebSocket：未接続")
        
        status_layout.addWidget(self.server_status_label)
        status_layout.addWidget(self.db_status_label)
        status_layout.addWidget(self.websocket_status_label)
        layout.addWidget(status_group)
        
        # 功能开关分组
        flags_group = QtWidgets.QGroupBox("機能スイッチ")
        flags_layout = QtWidgets.QVBoxLayout(flags_group)
        
        self.flags_table = QtWidgets.QTableWidget(0, 3)
        self.flags_table.setHorizontalHeaderLabels(["機能名", "状態", "操作"])
        flags_layout.addWidget(self.flags_table)
        layout.addWidget(flags_group)
        
        # 登录视图
        layout.addWidget(self.login_view)
        
        # 程序启动控制面板
        program_group = QtWidgets.QGroupBox("プログラム起動制御")
        program_layout = QtWidgets.QVBoxLayout(program_group)
        
        # 说明标签
        program_info_label = QtWidgets.QLabel("ログイン成功後、以下の独立プログラムを起動できます：")
        program_info_label.setStyleSheet("color: #666; font-style: italic;")
        program_layout.addWidget(program_info_label)
        
        # 程序启动按钮
        program_buttons = QtWidgets.QHBoxLayout()
        self.program_launch_btn = QtWidgets.QPushButton("従業員操作画面")
        self.program_launch_btn.setEnabled(False)
        self.program_launch_btn.setVisible(False)
        self.program_launch_btn.clicked.connect(lambda: self.feature_viewmodel.launch_program("program1"))
        self.program_launch_btn.setStyleSheet("background-color: #007bff; color: white; font-weight: bold; padding: 8px;")
        program_buttons.addWidget(self.program_launch_btn)
        
        self.program2_launch_btn = QtWidgets.QPushButton("PLC編集ツール")
        self.program2_launch_btn.setEnabled(False)
        self.program2_launch_btn.setVisible(False)
        self.program2_launch_btn.clicked.connect(lambda: self.feature_viewmodel.launch_program("program2"))
        self.program2_launch_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold; padding: 8px;")
        program_buttons.addWidget(self.program2_launch_btn)
        
        program_buttons.addStretch()
        program_layout.addLayout(program_buttons)
        
        # 程序状态显示
        self.program_status_label = QtWidgets.QLabel("プログラム機能を有効にするには、まずログインしてください")
        self.program_status_label.setStyleSheet("color: #888; font-size: 12px;")
        program_layout.addWidget(self.program_status_label)
        
        layout.addWidget(program_group)
        
        # 计数器分组
        counter_group = QtWidgets.QGroupBox("カウンターテスト")
        counter_layout = QtWidgets.QVBoxLayout(counter_group)
        
        # 第一行: btn1和文本框1
        row1_layout = QtWidgets.QHBoxLayout()
        self.btn1 = QtWidgets.QPushButton("btn1 (+1)")
        self.btn1.clicked.connect(self.on_btn1_clicked)
        row1_layout.addWidget(self.btn1)
        
        row1_layout.addWidget(QtWidgets.QLabel("カウント値1："))
        self.text_box1 = QtWidgets.QLineEdit()
        self.text_box1.setReadOnly(True)
        self.text_box1.setText("0")
        self.text_box1.setFixedWidth(80)
        row1_layout.addWidget(self.text_box1)
        
        counter_layout.addLayout(row1_layout)
        
        # 第二行: btn2和文本框2
        row2_layout = QtWidgets.QHBoxLayout()
        self.btn2 = QtWidgets.QPushButton("btn2 (+2)")
        self.btn2.clicked.connect(self.on_btn2_clicked)
        row2_layout.addWidget(self.btn2)
        
        row2_layout.addWidget(QtWidgets.QLabel("カウント値2："))
        self.text_box2 = QtWidgets.QLineEdit()
        self.text_box2.setReadOnly(True)
        self.text_box2.setText("0")
        self.text_box2.setFixedWidth(80)
        row2_layout.addWidget(self.text_box2)
        
        counter_layout.addLayout(row2_layout)
        
        # 第三行: 控制按钮
        controls_layout = QtWidgets.QHBoxLayout()
        self.refresh_counter_btn = QtWidgets.QPushButton("更新")
        self.refresh_counter_btn.clicked.connect(self.load_current_counter)
        controls_layout.addWidget(self.refresh_counter_btn)
        
        self.reset_btn = QtWidgets.QPushButton("リセット")
        self.reset_btn.clicked.connect(self.on_reset_counter)
        controls_layout.addWidget(self.reset_btn)
        
        controls_layout.addStretch()
        counter_layout.addLayout(controls_layout)
        
        layout.addWidget(counter_group)
        
        # 按钮区
        button_layout = QtWidgets.QHBoxLayout()
        
        self.refresh_btn = QtWidgets.QPushButton("状態更新")
        self.refresh_btn.clicked.connect(self.feature_viewmodel.refresh_server_status)
        
        self.xml_btn = QtWidgets.QPushButton("XMLデータインポート")
        self.xml_btn.clicked.connect(self.open_xml_dialog)
        
        self.odbc_connect_btn = QtWidgets.QPushButton("ODBC接続")
        self.odbc_connect_btn.clicked.connect(self.connect_odbc)
        
        self.odbc_btn = QtWidgets.QPushButton("ODBCデータクエリ")
        self.odbc_btn.clicked.connect(self.query_odbc_data)
        self.odbc_btn.setEnabled(False)
        
        self.minimize_btn = QtWidgets.QPushButton("トレイに最小化")
        self.minimize_btn.clicked.connect(self.hide)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.xml_btn)
        button_layout.addWidget(self.odbc_connect_btn)
        button_layout.addWidget(self.odbc_btn)
        button_layout.addWidget(self.minimize_btn)
        layout.addLayout(button_layout)
        
        # 日志区
        log_group = QtWidgets.QGroupBox("运行日志")
        log_layout = QtWidgets.QVBoxLayout(log_group)
        
        self.log_text = QtWidgets.QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        layout.addWidget(log_group)
    
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if event.type() == QtCore.QEvent.Type.WindowStateChange:
            if self.windowState() & QtCore.Qt.WindowState.WindowMinimized:
                QtCore.QTimer.singleShot(0, self.hide)
                self.client_app.tray_icon.showMessage(
                    "MySuite Client",
                    "已最小化到托盘，双击图标可恢复",
                    QtWidgets.QSystemTrayIcon.MessageIcon.Information,
                    2000
                )
        return super().eventFilter(obj, event)

    def closeEvent(self, event):
        """关闭事件"""
        # 询问用户是否要完全退出程序
        reply = QtWidgets.QMessageBox.question(
            self, 
            "退出确认", 
            "是否要完全退出程序？\n选择\"是\"将完全退出，选择\"否\"将隐藏到托盘。",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No,
            QtWidgets.QMessageBox.StandardButton.No
        )
        
        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            # 完全退出程序
            self.client_app.on_exit()
        else:
            # 隐藏到托盘
            event.ignore()
            self.hide()
            self.client_app.tray_icon.showMessage(
                "MySuite Client",
                "已隐藏到托盘，双击图标可恢复",
                QtWidgets.QSystemTrayIcon.MessageIcon.Information,
                2000
            )
    
    def log_message(self, message):
        """写入运行日志"""
        self.log_text.append(f"[{QtCore.QDateTime.currentDateTime().toString()}] {message}")
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    # 信号槽处理方法
    def on_login_successful(self, user: UserModel):
        """登录成功处理"""
        self.log_message(f"登录成功，欢迎 {user.employee_name}")
        
        # 设置当前用户
        self.auth_viewmodel.current_user = user
        self.feature_viewmodel.set_current_user(user)
        
        # 清空输入框
        self.login_view.clear_inputs()
        
        # 获取JWT token和权限信息
        self._get_employee_jwt_token_and_permissions(user.employee_id, user.employee_name)
        
        # 打开员工界面
        self.open_employee_interface(user.employee_id, user.employee_name)
    
    def on_login_failed(self, error_msg: str):
        """登录失败处理"""
        self.log_message(f"登录失败: {error_msg}")
        QtWidgets.QMessageBox.warning(self, "登录失败", error_msg)
        self.login_view.password_input.clear()
    
    def on_register_successful(self, success_msg: str):
        """注册成功处理"""
        self.log_message(f"硬件指纹注册成功")
        QtWidgets.QMessageBox.information(self, "注册成功", success_msg)
        self.login_view.password_input.clear()
        self.login_view.remember_password_checkbox.setChecked(False)
    
    def on_register_failed(self, error_msg: str):
        """注册失败处理"""
        self.log_message(f"注册失败: {error_msg}")
        QtWidgets.QMessageBox.warning(self, "注册失败", error_msg)
    
    def on_delete_registration_successful(self, success_msg: str):
        """删除注册成功处理"""
        self.log_message(f"硬件指纹删除成功")
        QtWidgets.QMessageBox.information(self, "删除成功", success_msg)
        self.login_view.clear_inputs()
    
    def on_delete_registration_failed(self, error_msg: str):
        """删除注册失败处理"""
        self.log_message(f"删除失败: {error_msg}")
        QtWidgets.QMessageBox.critical(self, "删除错误", f"删除失败: {error_msg}")
    
    def on_hardware_verification_failed(self, error_msg: str):
        """硬件验证失败处理"""
        self.log_message(f"硬件指纹验证失败: {error_msg}")
        QtWidgets.QMessageBox.warning(self, "验证失败", error_msg)
        self.login_view.password_input.clear()
    
    def on_program_launch_successful(self, program_name: str, pid: int):
        """程序启动成功处理"""
        self.log_message(f"{program_name}程序已启动 (PID: {pid})")
        self.program_status_label.setText(f"{program_name}已启动 (PID: {pid})")
        
        # 更新按钮状态
        if program_name == "员工操作界面":
            self.program_launch_btn.setText("重新启动员工操作界面")
        elif program_name == "PLC编程工具":
            self.program2_launch_btn.setText("重新启动PLC编程工具")
    
    def on_program_launch_failed(self, program_name: str, error_msg: str):
        """程序启动失败处理"""
        self.log_message(f"启动{program_name}失败: {error_msg}")
        QtWidgets.QMessageBox.critical(self, "启动失败", error_msg)
    
    def on_permissions_updated(self, permission_info: dict):
        """权限更新处理（保留用于信号槽连接）"""
        self._update_program_buttons_based_on_permissions(permission_info)
    
    def on_server_status_updated(self, connected: bool, status_text: str):
        """服务器状态更新处理"""
        if "服务器" in status_text:
            self.server_status_label.setText(status_text)
        elif "数据库" in status_text:
            self.db_status_label.setText(status_text)
        
        if connected:
            self.log_message(status_text)
        else:
            self.log_message(f"连接异常: {status_text}")
    
    def on_feature_flags_updated(self, flags: dict):
        """功能开关更新处理"""
        self.update_flags_display(flags)
    
    def update_websocket_status(self, connected):
        """更新WebSocket连接状态"""
        if connected:
            self.websocket_status_label.setText("WebSocket: 已连接")
            self.log_message("WebSocket 已连接")
        else:
            self.websocket_status_label.setText("WebSocket: 未连接")
            self.log_message("WebSocket 未连接")
    
    def on_employee_id_changed(self, employee_id: str):
        """员工ID变化处理"""
        if employee_id:
            # 尝试加载保存的密码
            saved_password = self.auth_viewmodel.load_saved_password(employee_id)
            if saved_password:
                self.login_view.set_password(saved_password)
                self.login_view.set_remember_password(True)
                self.log_message(f"已自动填入员工ID '{employee_id}' 的保存密码")
            else:
                # 清空密码框和复选框
                self.login_view.set_password("")
                self.login_view.set_remember_password(False)
        else:
            # 清空密码框和复选框
            self.login_view.set_password("")
            self.login_view.set_remember_password(False)
    
    def on_login_cancel(self):
        """登录取消处理"""
        self.login_view.clear_inputs()
        self.log_message("取消登录，最小化到托盘")
        self.hide()
    
    def on_async_request_finished(self, request_id: str, result: dict, error: object):
        """异步请求完成处理"""
        if error:
            self.log_message(f"请求失败 [{request_id}]: {error}")
            return
        
        # 根据request_id分发到不同的处理函数
        if request_id.startswith('btn1_'):
            self._handle_btn1_response(result)
        elif request_id.startswith('btn2_'):
            self._handle_btn2_response(result)
        elif request_id.startswith('reset_'):
            self._handle_reset_response(result)
        elif request_id.startswith('refresh_'):
            self._handle_refresh_response(result)
        else:
            self.log_message(f"未知请求类型: {request_id}")
    
    def _handle_btn1_response(self, result: dict):
        """处理btn1异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            message = data.get('message', '')
            self.text_box1.setText(str(n_value))
            self.log_message(f"btn1成功: {message}")
        else:
            self.log_message(f"btn1失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_btn2_response(self, result: dict):
        """处理btn2异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            message = data.get('message', '')
            self.text_box2.setText(str(n_value))
            self.log_message(f"btn2成功: {message}")
        else:
            self.log_message(f"btn2失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_reset_response(self, result: dict):
        """处理重置异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            self.text_box1.setText(str(n_value))
            self.text_box2.setText(str(n_value))
            self.log_message("计数器重置成功")
        else:
            self.log_message(f"重置失败: {result.get('status_code')} {result.get('data')}")
    
    def _handle_refresh_response(self, result: dict):
        """处理刷新异步响应"""
        if result.get('ok'):
            data = result.get('data', {})
            n_value = data.get('n', 0)
            self.text_box1.setText(str(n_value))
            self.text_box2.setText(str(n_value))
            self.log_message(f"当前计数器值: {n_value}")
        else:
            self.log_message(f"刷新失败: {result.get('status_code')} {result.get('data')}")
    
    def update_flags_display(self, flags):
        """更新功能开关显示"""
        self.flags_table.setRowCount(len(flags))
        for i, (name, enabled) in enumerate(flags.items()):
            self.flags_table.setItem(i, 0, QtWidgets.QTableWidgetItem(name))
            self.flags_table.setItem(i, 1, QtWidgets.QTableWidgetItem("已开启" if enabled else "已关闭"))
            
            toggle_btn = QtWidgets.QPushButton("切换")
            toggle_btn.clicked.connect(lambda checked, n=name: self.feature_viewmodel.toggle_feature_flag(n))
            self.flags_table.setCellWidget(i, 2, toggle_btn)
    
    def open_xml_dialog(self):
        """打开XML数据导入对话框"""
        dlg = Ui_InputDialog()
        dlg.exec()
    
    def connect_odbc(self):
        """连接ODBC"""
        self.log_message("尝试连接ODBC...")
        try:
            # 先检查ODBC状态
            status_resp = self.http_service.get_sync_client().get("/api/odbc/status", timeout=5)
            if not status_resp.get('ok'):
                self.log_message("无法获取ODBC状态")
                return
            
            status_data = status_resp.get('data', {}).get('data', {})
            if not status_data.get('platform_supported', False):
                msg = f"当前平台不支持ODBC功能\n平台: {status_data.get('platform', 'Unknown')}\npywin32可用: {status_data.get('pywin32_available', False)}"
                self.log_message(msg)
                QtWidgets.QMessageBox.warning(self, "ODBC连接", msg)
                return
            
            if status_data.get('is_connected', False):
                self.log_message("ODBC已经连接")
                self.update_odbc_ui_state(True)
                return
            
            # 尝试连接
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            resp = self.http_service.get_sync_client().post("/api/odbc/connect", timeout=10, headers=headers)
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    self.log_message("ODBC连接成功")
                    self.update_odbc_ui_state(True)
                    QtWidgets.QMessageBox.information(self, "ODBC连接", "ODBC连接成功！")
                else:
                    error_msg = data.get('message', '连接失败')
                    self.log_message(f"ODBC连接失败: {error_msg}")
                    QtWidgets.QMessageBox.warning(self, "ODBC连接", f"连接失败: {error_msg}")
            else:
                error_msg = f"HTTP错误: {resp.get('status_code')} {resp.get('data')}"
                self.log_message(f"ODBC连接失败: {error_msg}")
                QtWidgets.QMessageBox.warning(self, "ODBC连接", f"连接失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"连接异常: {e}"
            self.log_message(error_msg)
            QtWidgets.QMessageBox.critical(self, "ODBC连接", error_msg)
    
    def update_odbc_ui_state(self, connected: bool):
        """更新ODBC相关UI状态"""
        if connected:
            self.odbc_connect_btn.setText("断开 ODBC")
            self.odbc_connect_btn.clicked.disconnect()
            self.odbc_connect_btn.clicked.connect(self.disconnect_odbc)
            self.odbc_btn.setEnabled(True)
        else:
            self.odbc_connect_btn.setText("连接 ODBC")
            self.odbc_connect_btn.clicked.disconnect()
            self.odbc_connect_btn.clicked.connect(self.connect_odbc)
            self.odbc_btn.setEnabled(False)
    
    def disconnect_odbc(self):
        """断开ODBC连接"""
        self.log_message("断开ODBC连接...")
        try:
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            resp = self.http_service.get_sync_client().post("/api/odbc/disconnect", timeout=5, headers=headers)
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    self.log_message("ODBC连接已断开")
                    self.update_odbc_ui_state(False)
                    QtWidgets.QMessageBox.information(self, "ODBC连接", "ODBC连接已断开")
                else:
                    self.log_message(f"断开ODBC失败: {data.get('message', '未知错误')}")
            else:
                self.log_message(f"断开ODBC失败: HTTP {resp.get('status_code')}")
        except Exception as e:
            self.log_message(f"断开ODBC异常: {e}")

    def query_odbc_data(self):
        """查询ODBC数据"""
        self.log_message("查询ODBC数据...")
        try:
            token = self.client_app.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            
            resp = self.http_service.get_sync_client().get("/api/odbc/read", timeout=5, headers=headers)
            if resp.get('ok'):
                data = resp.get('data')
                text = "\n".join(str(row) for row in data) if isinstance(data, list) else str(data)
                self.log_message("ODBC查询成功")
            else:
                if resp.get('status_code') == 400:
                    text = "请先点击'连接ODBC'按钮建立连接"
                elif resp.get('status_code') == 501:
                    text = "当前平台不支持ODBC功能"
                else:
                    text = f"错误: {resp.get('status_code')} {resp.get('data')}"
                self.log_message(f"ODBC查询失败: {resp.get('status_code')}")
        except Exception as e:
            text = f"异常: {e}"
            self.log_message(f"ODBC异常: {e}")
        
        dlg = QtWidgets.QMessageBox()
        dlg.setWindowTitle("ODBC 数据查询")
        dlg.setText(text)
        dlg.exec()
    
    def on_btn1_clicked(self):
        """btn1点击处理"""
        self.log_message("点击 btn1，向服务器发送异步请求...")
        request_id = f"btn1_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.http_service.get_async_client().post_async("/api/counter/btn1", request_id)
    
    def on_btn2_clicked(self):
        """btn2点击处理"""
        self.log_message("点击 btn2，向服务器发送异步请求...")
        request_id = f"btn2_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.http_service.get_async_client().post_async("/api/counter/btn2", request_id)
    
    def on_reset_counter(self):
        """重置计数器"""
        self.log_message("重置计数器...")
        request_id = f"reset_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.http_service.get_async_client().post_async("/api/counter/reset", request_id)
    
    def load_current_counter(self):
        """手动刷新当前计数器值"""
        self.log_message("刷新计数器值...")
        request_id = f"refresh_{int(QtCore.QDateTime.currentSecsSinceEpoch())}"
        self.http_service.get_async_client().get_async("/api/counter/current", request_id)
    
    def load_last_used_credentials(self):
        """加载最近使用的凭据"""
        try:
            last_employee_id = self.auth_viewmodel.load_last_used_employee_id()
            if last_employee_id:
                self.login_view.set_employee_id(last_employee_id)
                self.log_message(f"已自动填入最近使用的员工ID: {last_employee_id}")
                # 触发ID变化事件以加载密码
                self.on_employee_id_changed(last_employee_id)
        except Exception as e:
            self.log_message(f"加载最近使用凭据失败: {e}")
    
    def _get_employee_jwt_token_and_permissions(self, employee_id: str, employee_name: str):
        """获取员工JWT token和权限信息"""
        try:
            # 获取JWT token
            token_result = self._get_employee_jwt_token(employee_id, employee_name)
            if not token_result or not token_result.get('token'):
                self.log_message("无法获取有效的登录token")
                return
            
            # 更新用户token
            self.auth_viewmodel.current_user.token = token_result['token']
            self.feature_viewmodel.current_user.token = token_result['token']
            
            self.log_message(f"成功获取JWT token: {token_result['token'][:50]}...")
            
            # 获取权限信息
            permission_info = self._get_server_permissions(employee_id)
            if permission_info:
                self._update_program_buttons_based_on_permissions(permission_info)
                self.log_message(f"登录成功！权限: {permission_info.get('permission', 'unknown')} - 已从服务器获取权限配置")
            else:
                # 服务器权限获取失败，隐藏所有程序按钮
                self._hide_all_program_buttons("サーバーから権限情報を取得できません")
                self.log_message("登录成功！但无法获取权限信息，所有程序功能已禁用")
                
        except Exception as e:
            self._hide_all_program_buttons(f"権限検証失敗: {e}")
            self.log_message(f"权限验证异常: {e}")
    
    def _get_employee_jwt_token(self, employee_id: str, employee_name: str) -> dict:
        """获取员工JWT token"""
        try:
            # 使用存储的密码进行认证
            password = self.auth_viewmodel.load_saved_password(employee_id)
            
            if not password:
                self.log_message("未找到存储的密码，无法自动获取token")
                return {}
            
            # 向认证服务请求token
            login_data = {
                "employee_id": employee_id,
                "password": password
            }
            
            # 使用认证服务客户端
            resp = self.http_service.get_sync_client_auth().post(
                "/login",
                timeout=5,
                json=login_data
            )
            
            if resp.get('ok'):
                result = resp.get('data', {})
                if result.get("success"):
                    token = result.get("access_token")
                    if token:
                        self.log_message("成功获取员工JWT token")
                        return {'token': token}
                    else:
                        self.log_message("认证服务响应中缺少access_token")
                else:
                    self.log_message(f"认证失败: {result.get('message', '未知错误')}")
            else:
                self.log_message(f"认证请求失败: HTTP {resp.get('status_code')}")
                
            return {}
                
        except Exception as e:
            self.log_message(f"获取JWT token异常: {e}")
            return {}
    
    def _get_server_permissions(self, employee_id: str) -> dict:
        """从服务器获取权限信息"""
        try:
            # 需要JWT token进行认证
            if not self.auth_viewmodel.current_user or not self.auth_viewmodel.current_user.token:
                self.log_message("没有有效的JWT token，无法获取权限信息")
                return {}
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.auth_viewmodel.current_user.token}"
            }
            
            # 使用认证服务客户端
            resp = self.http_service.get_sync_client_auth().get(
                f"/api/program/permissions/{employee_id}",
                timeout=5,
                headers=headers
            )
            
            if resp.get('ok'):
                return resp.get('data', {})
            else:
                self.log_message(f"获取权限信息失败: {resp.get('status_code')} {resp.get('data')}")
                return {}
                
        except Exception as e:
            self.log_message(f"获取权限信息异常: {e}")
            return {}
    
    def _update_program_buttons_based_on_permissions(self, permission_info: dict):
        """根据权限信息更新程序按钮状态"""
        try:
            accessible_programs = permission_info.get('accessible_programs', [])
            permission = permission_info.get('permission', 'unknown')
            permission_desc = permission_info.get('permission_description', permission)
            
            # 检查program1权限
            program1_accessible = any(p.get('program_name') == 'program1' for p in accessible_programs)
            if program1_accessible:
                self.program_launch_btn.setVisible(True)
                self.program_launch_btn.setEnabled(True)
                self.program_launch_btn.setText("従業員操作画面")
            else:
                self.program_launch_btn.setVisible(False)
            
            # 检查program2权限
            program2_accessible = any(p.get('program_name') == 'program2' for p in accessible_programs)
            if program2_accessible:
                self.program2_launch_btn.setVisible(True)
                self.program2_launch_btn.setEnabled(True)
                self.program2_launch_btn.setText("PLC編集ツール")
            else:
                self.program2_launch_btn.setVisible(False)
            
            # 更新状态标签
            total_programs = len(accessible_programs)
            if total_programs > 0:
                program_names = []
                for p in accessible_programs:
                    if p.get('program_name') == 'program1':
                        program_names.append('従業員操作画面')
                    elif p.get('program_name') == 'program2':
                        program_names.append('PLC編集ツール')
                
                self.program_status_label.setText(
                    f"ようこそ！{permission_desc} - 利用可能: {', '.join(program_names)}"
                )
            else:
                self.program_status_label.setText(f"{permission_desc} - プログラム機能なし")
                
        except Exception as e:
            self.log_message(f"更新程序按钮状态失败: {e}")
            self._hide_all_program_buttons(f"状态更新失败: {e}")
    
    def open_employee_interface(self, employee_id: str, employee_name: str):
        """打开员工操作界面"""
        self.log_message(f"打开员工界面: {employee_name} ({employee_id})")
        # 保持Launcher界面可见，不再隐藏
    
    def _hide_all_program_buttons(self, reason: str):
        """隐藏所有程序启动按钮"""
        self.program_launch_btn.setVisible(False)
        self.program2_launch_btn.setVisible(False)
        self.program_status_label.setText(f"プログラム機能無効: {reason}")

class ClientApp(QtWidgets.QApplication):
    """客户端应用（MVVM架构）"""
    
    # Signal to apply flags from a non-GUI thread
    apply_flags_signal = QtCore.Signal(dict)
    websocket_status_signal = QtCore.Signal(bool)

    def __init__(self, argv):
        super().__init__(argv)
        # 隐藏最后窗口关闭时退出
        self.setQuitOnLastWindowClosed(False)
        
        # 设置信号处理
        import signal
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 跨平台托盘图标处理
        if not QtWidgets.QSystemTrayIcon.isSystemTrayAvailable():
            QtWidgets.QMessageBox.critical(None, "错误", "当前系统不支持托盘图标")
            sys.exit(1)
        
        # 设置图标路径
        self.setup_icon_path()

        # 主窗口
        self.main_window = MainWindow(self)
        
        # 托盘
        self.tray_icon = QtWidgets.QSystemTrayIcon(QtGui.QIcon(str(self.icon_path)), self)
        self.tray_icon.activated.connect(self.on_tray_activated)
        menu = QtWidgets.QMenu()
        show_action = menu.addAction("显示主窗口")
        show_action.triggered.connect(self.show_main_window)
        menu.addSeparator()
        xml_action = menu.addAction("导入 XML 数据")
        xml_action.triggered.connect(self.open_input_dialog)
        odbc_connect_action = menu.addAction("连接 ODBC")
        odbc_connect_action.triggered.connect(self.main_window.connect_odbc)
        odbc_action = menu.addAction("查询 ODBC 数据")
        odbc_action.triggered.connect(self.on_click_odbc)
        menu.addSeparator()
        exit_action = menu.addAction("退出程序")
        exit_action.triggered.connect(self.on_exit)
        self.tray_icon.setContextMenu(menu)
        self.tray_icon.show()
        self.tray_icon.showMessage(
            "MySuite Client",
            "客户端已启动，点击托盘图标可显示主窗口",
            QtWidgets.QSystemTrayIcon.MessageIcon.Information,
            3000
        )

        self.feature_manager = FeatureManager()

        # 信号连接
        self.apply_flags_signal.connect(self._apply_flags_slot)
        self.websocket_status_signal.connect(self.main_window.update_websocket_status)

        # 异步处理相关
        self.loop = asyncio.new_event_loop()
        self.async_thread = threading.Thread(target=self.loop.run_forever, daemon=True)
        self.async_thread.start()
        
        # 添加退出标志
        self._shutdown_event = threading.Event()
        
        # 设置HTTP客户端的事件循环
        self.main_window.http_service.set_event_loop(self.loop)

        # 加载本地缓存并初始化
        config_service = ConfigService()
        local_flags = config_service.load_local_flags()
        self.apply_flags_signal.emit(local_flags)
        
        # 启动WebSocket监听任务
        self.websocket_task = asyncio.run_coroutine_threadsafe(self.fetch_and_listen_flags(), self.loop)
        
        # 显示主窗口
        self.show_main_window()

    def setup_icon_path(self):
        """设置跨平台图标路径"""
        basedir = Path(__file__).parent
        
        resources_dir = basedir / "resources" / "icons"
        resources_dir.mkdir(parents=True, exist_ok=True)
        
        self.icon_path = resources_dir / "app.png"
        
        if not self.icon_path.exists():
            self.create_default_icon()

    def create_default_icon(self):
        """创建默认图标"""
        try:
            pixmap = QtGui.QPixmap(32, 32)
            pixmap.fill(QtGui.QColor(0, 120, 215))
            
            painter = QtGui.QPainter(pixmap)
            painter.setPen(QtGui.QColor(255, 255, 255))
            painter.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Weight.Bold))
            painter.drawText(pixmap.rect(), QtCore.Qt.AlignmentFlag.AlignCenter, "MS")
            painter.end()
            
            pixmap.save(str(self.icon_path), "PNG")
            print(f"Created default icon at: {self.icon_path}")
        except Exception as e:
            print(f"Failed to create default icon: {e}")
            self.icon_path = None

    def show_main_window(self):
        """显示主窗口"""
        self.main_window.show()
        self.main_window.raise_()
        self.main_window.activateWindow()

    async def fetch_and_listen_flags(self):
        """获取并监听功能开关"""
        try:
            session = requests.Session()
            session.trust_env = False
            
            resp = session.get(f"{SERVER_HTTP_BASE}/api/feature_flags/", timeout=5, verify=False)
            if resp.ok:
                flags = resp.json()
                self.apply_flags_signal.emit(flags)
                print(f"Successfully fetched initial flags: {flags}")
            else:
                print(f"Failed to fetch flags: {resp.status_code}")
        except Exception as e:
            print(f"Error fetching initial flags: {e}")

        # WebSocket 连接
        token = self.get_jwt_token()
        ws_url = SERVER_WS_URL + token
        while not self._shutdown_event.is_set():
            try:
                import ssl
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                async with connect(ws_url, ssl=ssl_context) as ws:
                    print("WebSocket connection established.")
                    self.websocket_status_signal.emit(True)
                    while not self._shutdown_event.is_set():
                        try:
                            # 使用asyncio.wait_for来添加超时，这样可以定期检查退出标志
                            msg = await asyncio.wait_for(ws.recv(), timeout=1.0)
                            data = json.loads(msg)
                            self.apply_flags_signal.emit(data)
                        except asyncio.TimeoutError:
                            # 超时是正常的，继续检查退出标志
                            continue
                        except Exception as e:
                            print(f"Error processing WebSocket message: {e}")
                            break
            except Exception as e:
                if not self._shutdown_event.is_set():
                    print(f"WebSocket connection error: {e}. Retrying in 5s.")
                    self.websocket_status_signal.emit(False)
                    # 使用asyncio.sleep替代，并检查退出标志
                    for _ in range(50):  # 5秒，每0.1秒检查一次
                        if self._shutdown_event.is_set():
                            break
                        await asyncio.sleep(0.1)
        
        print("WebSocket listener stopped due to shutdown request.")

    @QtCore.Slot(dict)
    def _apply_flags_slot(self, flags: dict):
        print(f"Applying flags from Qt slot: {flags}")
        
        # Handle different flag formats
        if 'name' in flags and 'enabled' in flags:  # Format from websocket
            flag_update = {flags['name']: flags['enabled']}
        else:  # Format from initial HTTP GET
            flag_update = flags

        self.feature_manager.apply_flags(flag_update)
        
        # Update main window display
        self.main_window.feature_viewmodel.update_feature_flags(flag_update)
        
        # Update local cache
        config_service = ConfigService()
        current_cached_flags = config_service.load_local_flags()
        current_cached_flags.update(flag_update)
        config_service.save_local_flags(current_cached_flags)

    def open_input_dialog(self):
        dlg = Ui_InputDialog()
        dlg.exec()

    def on_click_odbc(self):
        try:
            session = requests.Session()
            session.trust_env = False
            token = self.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}"}
            resp = session.get(f"{SERVER_HTTP_BASE}/api/odbc/read/", headers=headers, timeout=5, verify=False)
            if resp.ok:
                data = resp.json()
                text = "\n".join(str(row) for row in data) if isinstance(data, list) else str(data)
            else:
                text = f"错误: {resp.status_code} {resp.text}"
        except Exception as e:
            text = f"异常: {e}"
        dlg = QtWidgets.QMessageBox()
        dlg.setWindowTitle("ODBC 数据查询")
        dlg.setText(text)
        dlg.exec()

    def on_exit(self):
        print("Exit requested. Starting graceful shutdown...")
        
        # 设置退出标志
        self._shutdown_event.set()

        # ==================== 新增代码 ====================
        # 1. 停止所有由Launcher启动的外部子进程
        # FeatureViewModel实例在MainWindow中，所以通过它来访问
        if self.main_window and hasattr(self.main_window, 'feature_viewmodel'):
            print("Stopping all external programs...")
            self.main_window.feature_viewmodel.stop_all_programs()
        # ================================================
        

        # 停止所有功能模块
        self.feature_manager.stop_all()
        
        # 清理HTTP客户端
        self.main_window.http_service.close()
        
        # 取消WebSocket任务
        if hasattr(self, 'websocket_task') and self.websocket_task:
            try:
                self.websocket_task.cancel()
                print("WebSocket task cancelled.")
            except Exception as e:
                print(f"Error cancelling WebSocket task: {e}")
        
        # 停止事件循环
        if self.loop and self.loop.is_running():
            try:
                # 使用call_soon_threadsafe来安全地停止事件循环
                self.loop.call_soon_threadsafe(self.loop.stop)
                print("Event loop stop requested.")
            except Exception as e:
                print(f"Error stopping event loop: {e}")
        
        # 等待异步线程结束（最多等待3秒）
        if hasattr(self, 'async_thread') and self.async_thread.is_alive():
            print("Waiting for async thread to finish...")
            self.async_thread.join(timeout=3.0)
            if self.async_thread.is_alive():
                print("Warning: Async thread did not finish within timeout.")
            else:
                print("Async thread finished successfully.")
        
        # 隐藏托盘图标
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        
        print("Shutdown complete. Exiting application.")
        QtCore.QCoreApplication.exit()

    def get_jwt_token(self) -> str:
        SECRET_KEY = "your-very-secret-signing-key"
        ALGORITHM = "HS256"
        import time
        from jose import jwt
        to_encode = {"sub": "client_user", "exp": time.time() + 3600}
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    def on_tray_activated(self, reason):
        if reason == QtWidgets.QSystemTrayIcon.ActivationReason.Trigger:
            self.show_main_window()

    def _signal_handler(self, signum, frame):
        """处理系统信号"""
        print(f"Received signal {signum}. Initiating graceful shutdown...")
        # 使用QTimer来确保在主线程中调用on_exit
        QtCore.QTimer.singleShot(0, self.on_exit)

# 跨平台启动脚本
if __name__ == "__main__":
    current_dir = Path(__file__).parent
    modules_dir = current_dir / "modules"
    utils_dir = current_dir / "utils"
    
    if modules_dir.exists():
        sys.path.append(str(modules_dir))
    if utils_dir.exists():
        sys.path.append(str(utils_dir))

    print(f"Starting MySuite Client on {platform.system()} {platform.release()}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {current_dir}")
    
    app = ClientApp(sys.argv)
    sys.exit(app.exec()) 