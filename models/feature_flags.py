from dataclasses import dataclass
from typing import Dict, List

@dataclass
class FeatureFlagModel:
    """功能开关数据模型"""
    name: str
    enabled: bool
    description: str = ""
    
    @classmethod
    def from_dict(cls, data: Dict[str, any]) -> 'FeatureFlagModel':
        """从字典创建功能开关模型"""
        return cls(
            name=data.get('name', ''),
            enabled=data.get('enabled', False),
            description=data.get('description', '')
        )
    
    def to_dict(self) -> Dict[str, any]:
        """转换为字典"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'description': self.description
        }

@dataclass
class FeatureFlagsState:
    """功能开关状态模型"""
    flags: Dict[str, bool]
    last_updated: str = ""
    
    def get_flag(self, name: str) -> bool:
        """获取指定功能开关状态"""
        return self.flags.get(name, False)
    
    def set_flag(self, name: str, enabled: bool):
        """设置功能开关状态"""
        self.flags[name] = enabled
    
    def get_enabled_flags(self) -> List[str]:
        """获取所有启用的功能开关"""
        return [name for name, enabled in self.flags.items() if enabled]
    
    def get_disabled_flags(self) -> List[str]:
        """获取所有禁用的功能开关"""
        return [name for name, enabled in self.flags.items() if not enabled] 