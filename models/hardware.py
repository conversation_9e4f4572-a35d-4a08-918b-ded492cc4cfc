from dataclasses import dataclass
from typing import Dict, Any
from datetime import datetime

@dataclass
class HardwareFingerprintModel:
    """硬件指纹数据模型"""
    mac_address: str
    motherboard_uuid: str
    machine_id: str
    cpu_info: str
    disk_serial: str
    platform: str
    timestamp: datetime
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'HardwareFingerprintModel':
        """从字典创建硬件指纹模型"""
        return cls(
            mac_address=data.get('mac_address', ''),
            motherboard_uuid=data.get('motherboard_uuid', ''),
            machine_id=data.get('machine_id', ''),
            cpu_info=data.get('cpu_info', ''),
            disk_serial=data.get('disk_serial', ''),
            platform=data.get('platform', ''),
            timestamp=datetime.fromisoformat(data.get('timestamp', datetime.now().isoformat()))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'mac_address': self.mac_address,
            'motherboard_uuid': self.motherboard_uuid,
            'machine_id': self.machine_id,
            'cpu_info': self.cpu_info,
            'disk_serial': self.disk_serial,
            'platform': self.platform,
            'timestamp': self.timestamp.isoformat()
        } 