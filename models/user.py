from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class UserModel:
    """用户数据模型"""
    employee_id: str
    employee_name: str
    token: Optional[str] = None
    login_time: Optional[datetime] = None
    permissions: Optional[list] = None
    
    def is_logged_in(self) -> bool:
        """检查用户是否已登录"""
        return self.token is not None and self.employee_id is not None
    
    def has_permission(self, program_name: str) -> bool:
        """检查用户是否有特定程序权限"""
        if not self.permissions:
            return False
        return any(p.get('program_name') == program_name for p in self.permissions) 