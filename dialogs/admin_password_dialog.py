from PySide6.QtWidgets import QDialog
from client.launcher.ui.ui_admin_password_dialog import Ui_AdminPasswordDialog

class AdminPasswordDialog(QDialog):
    """管理员密码输入对话框的逻辑类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.ui = Ui_AdminPasswordDialog()
        self.ui.setup_ui(self)
        
        self._connect_signals()

    def _connect_signals(self):
        """连接UI组件的信号到QDialog的内置槽"""
        self.ui.ok_button.clicked.connect(self.accept)
        self.ui.cancel_button.clicked.connect(self.reject)
        self.ui.password_input.returnPressed.connect(self.accept)
            
    def get_password(self) -> str:
        """获取输入的密码"""
        return self.ui.password_input.text()