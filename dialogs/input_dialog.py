from PySide6.QtWidgets import QDialog
from client.launcher.ui.ui_input_dialog import Ui_InputDialog

class InputDialog(QDialog):
    """XML数据输入对话框的逻辑类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ui = Ui_InputDialog()
        self.ui.setup_ui(self)
        
        self._connect_signals()
    
    def _connect_signals(self):
        self.ui.send_btn.clicked.connect(self.on_send)

    def on_send(self):
        """
        发送XML数据。
        注意：如您的分析所述，将网络请求等业务逻辑放在视图/对话框中
        不是最佳实践。理想情况下，此方法应发出一个信号，由
        ViewModel或Controller来处理网络请求。
        """
        client_id = self.ui.client_id_edit.text().strip()
        key = self.ui.key_edit.text().strip()
        value = self.ui.value_edit.text().strip()
        
        if not client_id or not key:
            self.ui.result_text.setPlainText("Client ID 和字段名不能为空")
            return
        
        data = {key: value}
        try:
            # 这是一个业务逻辑，理想情况应该移出对话框
            import requests
            session = requests.Session()
            session.trust_env = False
            
            token = self.get_jwt_token()
            headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
            payload = {"client_id": client_id, "data": data}
            resp = session.post(
                "https://localhost/api/xml/write/",
                headers=headers,
                json=payload,
                timeout=5,
                verify=False
            )
            if resp.status_code == 200:
                self.ui.result_text.setPlainText(f"发送成功: {resp.json()}")
            else:
                self.ui.result_text.setPlainText(f"发送失败: {resp.status_code} {resp.text}")
        except Exception as e:
            self.ui.result_text.setPlainText(f"发送错误: {e}")

    def get_jwt_token(self) -> str:
        """获取JWT令牌。这也是业务逻辑，应移出。"""
        SECRET_KEY = "your-very-secret-signing-key"
        ALGORITHM = "HS256"
        import time
        from jose import jwt
        to_encode = {"sub": "client_user", "exp": time.time() + 3600}
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)