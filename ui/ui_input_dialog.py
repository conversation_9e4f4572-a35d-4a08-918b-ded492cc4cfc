
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QTextEdit
)

class Ui_InputDialog(object):
    """纯UI定义类：XML数据输入对话框"""
    def setup_ui(self, InputDialog):
        InputDialog.setWindowTitle("导入 XML 数据")
        InputDialog.resize(400, 300)

        layout = QVBoxLayout(InputDialog)

        layout.addWidget(QLabel("Client ID:", InputDialog))
        self.client_id_edit = QLineEdit(InputDialog)
        self.client_id_edit.setPlaceholderText("client_id (如 1, 2-1)")
        layout.addWidget(self.client_id_edit)

        layout.addWidget(QLabel("字段名:", InputDialog))
        self.key_edit = QLineEdit(InputDialog)
        self.key_edit.setPlaceholderText("字段名 (如 username)")
        layout.addWidget(self.key_edit)

        layout.addWidget(QLabel("字段值:", InputDialog))
        self.value_edit = QLineEdit(InputDialog)
        self.value_edit.setPlaceholderText("字段值 (如 alice)")
        layout.addWidget(self.value_edit)

        self.send_btn = QPushButton("发送 XML", InputDialog)
        layout.addWidget(self.send_btn)

        self.result_text = QTextEdit(InputDialog)
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
