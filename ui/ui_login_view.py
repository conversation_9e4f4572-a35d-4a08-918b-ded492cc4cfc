# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'login_view.ui'
#
# Created by: PySide6 UI code generator 6.x.x
#
# WARNING: Any manual changes made to this file will be lost when pyuic6 is
# run again.  Do not edit this file unless you know what you are doing.

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QCheckBox, QGroupBox
)

class Ui_LoginView(object):
    """
    纯UI定义类，只负责创建和布局UI组件。
    这个类不包含任何事件处理逻辑。
    """
    def setup_ui(self, LoginView):
        """
        在传入的 QWidget (LoginView) 上构建UI。
        所有UI组件都成为这个 Ui_LoginView 实例的属性。
        """
        LoginView.setObjectName("LoginView")
        layout = QVBoxLayout(LoginView)
        
        # 登录分组
        self.login_group = QGroupBox("ユーザーログイン")
        login_layout = QVBoxLayout(self.login_group)
        
        # 登录控件行1：ID输入
        login_row1 = QHBoxLayout()
        login_row1.addWidget(QLabel("従業員ID："))
        self.id_input = QLineEdit()
        self.id_input.setPlaceholderText("従業員IDを入力してください")
        self.id_input.setFixedWidth(150)
        login_row1.addWidget(self.id_input)
        login_row1.addStretch()
        login_layout.addLayout(login_row1)
        
        # 登录控件行2：密码输入
        login_row2 = QHBoxLayout()
        login_row2.addWidget(QLabel("パスワード："))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("パスワードを入力してください")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setFixedWidth(150)
        login_row2.addWidget(self.password_input)
        login_row2.addStretch()
        login_layout.addLayout(login_row2)
        
        # 记住密码复选框行
        remember_row = QHBoxLayout()
        self.remember_password_checkbox = QCheckBox("パスワードを記憶")
        self.remember_password_checkbox.setToolTip("選択するとパスワードが安全に保存され、次回起動時に自動入力されます")
        remember_row.addWidget(self.remember_password_checkbox)
        remember_row.addStretch()
        login_layout.addLayout(remember_row)
        
        # 登录控件行3：按钮
        login_buttons = QHBoxLayout()
        self.login_confirm_btn = QPushButton("ログイン確認")
        self.register_btn = QPushButton("登録")
        self.register_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold;")
        self.delete_registration_btn = QPushButton("登録削除")
        self.delete_registration_btn.setStyleSheet("background-color: #dc3545; color: white; font-weight: bold;")
        self.login_cancel_btn = QPushButton("キャンセル")
        
        login_buttons.addWidget(self.login_confirm_btn)
        login_buttons.addWidget(self.register_btn)
        login_buttons.addWidget(self.delete_registration_btn)
        login_buttons.addWidget(self.login_cancel_btn)
        login_buttons.addStretch()
        login_layout.addLayout(login_buttons)
        
        layout.addWidget(self.login_group)