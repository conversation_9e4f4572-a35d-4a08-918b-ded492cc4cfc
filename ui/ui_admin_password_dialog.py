from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton
)
from PySide6.QtCore import Qt

class Ui_AdminPasswordDialog(object):
    """纯UI定义类：管理员密码对话框"""
    def setup_ui(self, AdminPasswordDialog):
        AdminPasswordDialog.setWindowTitle("管理员密码验证")
        AdminPasswordDialog.setModal(True)
        AdminPasswordDialog.resize(300, 150)
        
        layout = QVBoxLayout(AdminPasswordDialog)
        
        info_label = QLabel("请输入管理员密码以确认删除操作：")
        info_label.setStyleSheet("font-weight: bold; color: #dc3545; margin: 10px;")
        layout.addWidget(info_label)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("请输入管理员密码")
        layout.addWidget(self.password_input)
        
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确认")
        self.ok_button.setStyleSheet("background-color: #dc3545; color: white; font-weight: bold;")
        
        self.cancel_button = QPushButton("取消")
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        self.password_input.setFocus()
