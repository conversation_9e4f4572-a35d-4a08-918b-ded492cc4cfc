from PySide6.QtWidgets import QWidget, QMessageBox
from PySide6.QtCore import Signal

# 从ui模块导入纯UI定义
from ..ui.ui_login_view import Ui_LoginView 
# 从dialogs模块导入重构后的对话框
#from .dialogs import AdminPasswordDialog
from ..ui.ui_admin_password_dialog import Ui_AdminPasswordDialog


class LoginView(QWidget):
    """
    登录视图的逻辑部分 (代码隐藏)。
    它使用 Ui_LoginView 来构建UI，并负责所有事件处理。
    """
    
    # 信号定义保持不变，这是视图的“公开接口”
    login_requested = Signal(str, str, bool)
    register_requested = Signal(str, str)
    delete_registration_requested = Signal(str, str)
    employee_id_changed = Signal(str)
    login_cancelled = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 1. 实例化UI定义类
        self.ui = Ui_LoginView()
        # 2. 调用setup_ui，将自身(self)传递给它来构建UI
        self.ui.setup_ui(self)
        # 3. 连接内部UI组件的信号到本类的方法
        self._connect_signals()

    def _connect_signals(self):
        """连接由 Ui_LoginView 创建的UI组件的信号"""
        self.ui.login_confirm_btn.clicked.connect(self._on_login_confirm)
        self.ui.register_btn.clicked.connect(self._on_register_clicked)
        self.ui.delete_registration_btn.clicked.connect(self._on_delete_registration_clicked)
        self.ui.login_cancel_btn.clicked.connect(self.login_cancelled)
        self.ui.id_input.textChanged.connect(self._on_employee_id_changed)
    
    # --- 私有槽函数，处理原始UI事件并转换为抽象信号 ---
    
    def _on_login_confirm(self):
        """登录确认按钮处理"""
        employee_id = self.ui.id_input.text().strip()
        password = self.ui.password_input.text()
        remember = self.ui.remember_password_checkbox.isChecked()
        
        if not employee_id:
            QMessageBox.warning(self, "登录", "请输入员工ID")
            return
        if not password:
            QMessageBox.warning(self, "登录", "请输入密码")
            return
            
        self.login_requested.emit(employee_id, password, remember)
    
    def _on_register_clicked(self):
        """注册按钮处理"""
        employee_id = self.ui.id_input.text().strip()
        password = self.ui.password_input.text()
        
        if not employee_id:
            QMessageBox.warning(self, "注册", "请输入员工ID")
            return
        if not password:
            QMessageBox.warning(self, "注册", "请输入密码")
            return
        
        reply = QMessageBox.question(
            self, "硬件指纹注册", 
            f"确定要为员工ID '{employee_id}' 注册硬件指纹吗？\n\n"
            "注册后，此设备将与该员工ID绑定。\n"
            "如果更换设备，需要重新注册。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.register_requested.emit(employee_id, password)
    
    def _on_delete_registration_clicked(self):
        """删除注册按钮处理"""
        employee_id = self.ui.id_input.text().strip()
        
        if not employee_id:
            QMessageBox.warning(self, "删除注册", "请输入员工ID")
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除员工ID '{employee_id}' 的硬件指纹注册吗？\n\n"
            "此操作需要管理员密码确认。\n"
            "删除后该员工需要重新注册才能登录。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            password_dialog = Ui_AdminPasswordDialog(self)
            if password_dialog.exec() == QMessageBox.DialogCode.Accepted:
                admin_password = password_dialog.get_password()
                self.delete_registration_requested.emit(employee_id, admin_password)
    
    def _on_employee_id_changed(self):
        """员工ID变化处理，转发信号"""
        self.employee_id_changed.emit(self.ui.id_input.text().strip())

    # --- 公开方法，供外部调用以更新UI ---
    
    def clear_inputs(self):
        self.ui.id_input.clear()
        self.ui.password_input.clear()
        self.ui.remember_password_checkbox.setChecked(False)
    
    def set_employee_id(self, employee_id: str):
        self.ui.id_input.setText(employee_id)
    
    def set_password(self, password: str):
        self.ui.password_input.setText(password)
    
    def set_remember_password(self, remember: bool):
        self.ui.remember_password_checkbox.setChecked(remember)

    # --- 公开方法，供外部调用以获取UI状态 ---

    def get_employee_id(self) -> str:
        return self.ui.id_input.text().strip()

    def get_password(self) -> str:
        return self.ui.password_input.text()

    def get_remember_password(self) -> bool:
        return self.ui.remember_password_checkbox.isChecked()