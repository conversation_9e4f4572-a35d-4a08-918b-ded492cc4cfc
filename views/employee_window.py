# program1_mvvm/views/employee_window.py (1,000行)
from .main_tab_mixin import MainTabMixin
from .chat_tab_mixin import ChatTabMixin
from .video_tab_mixin import VideoTabMixin

class EmployeeInterfaceWindow(QtWidgets.QMainWindow, MainTabMixin, ChatTabMixin, VideoTabMixin):
    # 信号定义 (保留)
    update_table1_signal = Signal(list)
    update_table3_signal = Signal(list)
    update_chart_signal = Signal(dict, str, bool)
    update_months_combo_signal = Signal(list)
    update_department_signal = Signal(str)
    notification_connection_status_signal = Signal(bool)
    notification_message_signal = Signal(dict)
    

            
    def __init__(self, employee_id: str, employee_name: str, token: str = None, main_window=None):
        super().__init__()

        self.employee_id = employee_id
        self.employee_name = employee_name
        self.token = token
        self.main_window = main_window
            # 初始化颜色主题管理器
        self.color_theme = ColorTheme()

        # 20250708 + Server5 API客户端配置（本地运行）
        self.server5_base_url = "http://localhost:8009"
        self.server5_client = Server5APIClient(self.server5_base_url)
        self.is_server5_enabled = True  # 是否启用Server5功能

        # 20250708 + 删除Server5-2相关配置，不再需要timeprotab采集凭据
        
        # HTTP客户端初始化 - 简化为仅使用必要的客户端
        # 修改4：支持https和http
        base_url = "https://localhost"
        if hasattr(self, 'main_window') and self.main_window:
            # 从主窗口获取配置
            pass
        
        # 主要的HTTP客户端
        self.async_http_client = SimpleAsyncHTTPClient(base_url)
        self.sync_http_client = SimpleSyncHTTPClient(base_url)
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 使用Server5专用客户端
        if self.is_server5_enabled:
            self.server5_async_client = SimpleAsyncHTTPClient(self.server5_base_url)
        
        # 连接异步请求完成信号
        self.async_http_client.request_finished.connect(self.on_async_request_finished)
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - 连接Server5客户端信号
        if self.is_server5_enabled:
            self.server5_async_client.request_finished.connect(self.on_server5_request_finished)
        
        # 表格3相关属性
        self.table3_current_month = QtCore.QDate.currentDate().addDays(-QtCore.QDate.currentDate().day() + 1)          # 月初
        self.table3_data_source = 'entries'  # #20250711+10：15+修改的主题 - 默认使用entries，不再使用xml
        self.table3_current_data = []  # 当前Table3显示的数据
        
        # #20250711+12：45+修改的主题@program1.py - 添加各区域的当前月份追踪变量
        self.table1_current_month = datetime.now()  # Table1当前显示的月份
        self.chart_current_month = datetime.now()   # Chart当前显示的月份
        
        # #20250711+13：10+修改的主题 - 添加按键状态跟踪变量，初始化时当月按键是激活状态
        self.table1_active_button = 'curr'  # Table1当前激活的按键：'prev', 'curr', 'next'
        self.table3_active_button = 'curr'  # Table3当前激活的按键：'prev', 'curr', 'next'  
        self.chart_active_button = 'curr'   # Chart当前激活的按键：'prev', 'curr', 'next'
        
        # #20250711+17：00+修改的主题 - 添加全局月份偏移量跟踪变量
        self.global_active_button = 'curr'  # 全局当前激活的按键：'prev', 'curr', 'next'
        self.global_month_offset = 0  # 全局月份偏移量（0=当月，-1=上个月，-2=2个月前，+1=下个月，+2=2个月后）
        
        # 2025/06/27+12：18 实现program1与Server5 entries分区表的集成 - entries操作状态管理
        self.current_editing_db_id = None  # 当前编辑中的entry ID
        self.is_editing_entry = False  # 是否处于编辑模式
        self.pending_operations = []  # 待处理的操作队列
        
        # 2025/06/26.11:50+分离ui操作 - 判断是否为独立运行模式
        self.is_standalone = (main_window is None)  # 如果没有主窗口引用，则为独立运行
        
        # 通知相关 - 2025/07/16 新增：MDB同步状态通知
        self.notification_connected = False
        self.notification_websocket = None
        self.notification_connect_thread = None
        
        self.setup_ui()
        
        # 信号连接
        #self.update_table1_signal.connect(self._update_table1_ui)
        self.update_table3_signal.connect(self._update_table3_ui)
        #self.update_months_signal.connect(self._update_months_ui)
        #self.update_chart_signal.connect(self._update_chart_ui)
        self.update_chart_signal.connect(self._update_chart_ui)
        self.update_table1_signal.connect(self._update_table1_ui)

        # 信号连接
        self.update_months_combo_signal.connect(self._update_months_combo_ui)
        self.update_department_signal.connect(self._update_department_ui)
        
        # 信号连接到对应的槽函数
        self.notification_connection_status_signal.connect(self.update_notification_connection_status)
        self.notification_message_signal.connect(self.handle_notification_message)


        # —— DateModel 初始化 & 信号绑定 —— 
        self.model = DateModel(self.date_label.text())
        self.model.dateChanged.connect(self.date_label.setText)
        self.model.dateChanged.connect(self.on_date_changed)

        # 修改 b2 布局微调 , 使用比例: 设置窗口初始尺寸
        self.setWindowTitle(f"従業員操作画面 - {self.employee_name}")
        self.resize(int(1920 * 0.9), int(1080 * 0.9)) # 设置初始尺寸为1152x864
        
        # 20250717+10-12+区域管理 - 初始化区域管理器
        self.region_manager = RegionManager()
        self.region_manager.style_manager = StyleManager()  # 添加样式管理器
        
        # 监听窗口关闭事件
        self.installEventFilter(self)
        
        # 设置传感器数据定时器
        self.sensor_timer = QtCore.QTimer()
        self.sensor_timer.timeout.connect(self.fetch_sensor_data)
        self.sensor_timer.start(500)  # 每5秒更新一次传感器数据
        
        # 立即获取一次传感器数据
        self.fetch_sensor_data()

        # 20250718+初始化区域创建 - 在界面初始化时立即创建区域
        self._initialize_regions()

        # 初始化一括処理按钮样式
        if hasattr(self, 'bulk_process_btn'):
            self.bulk_process_btn.setStyleSheet(self.color_theme.get_button_style("bulk_process", False))
        # 修改 b10/b11: 登录后自动加载数据
        #  修改 b20 跳转界面
        QtCore.QTimer.singleShot(200, self.auto_load_initial_data)


    def setup_ui(self):
        # 简化的UI设置，调用各个Mixin的UI设置方法
        self._setup_main_tab_ui()
        self._setup_chat_tab_ui()
        self._setup_video_tab_ui()
        
    
    
    def _initialize_button_colors(self):
        """#20250711+13：10+修改的主题 - 初始化所有按键颜色，当月按键设为浅绿色"""
        self._update_button_colors('table1', 'curr')
        self._update_button_colors('table3', 'curr')
        self._update_button_colors('chart', 'curr')
        # #20250711+16：40+修改的主题 - 初始化全局控制面板按键颜色
        self._update_button_colors('global', 'curr')
        
    def return_to_main(self):
        """返回主界面"""
        self.log_employee_message("メイン画面に戻ります")
        # 2025/06/26.11:50+分离ui操作 - 根据运行模式处理返回
        if self.is_standalone:
            # 独立运行模式：直接关闭程序
            self.close()
        else:
            # 集成运行模式：返回主窗口
            if self.main_window:
                self.main_window.show()
                self.main_window.raise_()
                self.main_window.activateWindow()
            self.close()
    
    def logout(self):
        """退出登录"""
        if QtWidgets.QMessageBox.question(self, "ログアウト", f"ログアウトしてもよろしいですか？\n現在のユーザー: {self.employee_name}") == QtWidgets.QMessageBox.StandardButton.Yes:
            self.log_employee_message(f"従業員 {self.employee_name} がログアウトしました")
            self.main_window.log_message(f"従業員 {self.employee_name} ({self.employee_id}) がログアウトしました")
            self.return_to_main()
    
    def closeEvent(self, event):
        """20250708 + 处理窗口关闭事件 - 修复连接清理问题"""
        try:
            print("开始清理Program1资源...")
            
            # 1. 关闭所有HTTP客户端
            if hasattr(self, 'server5_client') and self.server5_client:
                self.server5_client.close()
            
            if hasattr(self, 'async_http_client') and self.async_http_client:
                try:
                    # 异步客户端需要异步关闭
                    if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                        asyncio.run_coroutine_threadsafe(self.async_http_client.close(), self.loop)
                    else:
                        # 如果没有运行的事件循环，创建一个新的来关闭
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.async_http_client.close())
                        loop.close()
                except Exception as e:
                    print(f"关闭async_http_client时出错: {e}")
            
            if hasattr(self, 'sync_http_client') and self.sync_http_client:
                self.sync_http_client.close()
            
            if hasattr(self, 'server5_async_client') and self.server5_async_client:
                try:
                    if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                        asyncio.run_coroutine_threadsafe(self.server5_async_client.close(), self.loop)
                    else:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(self.server5_async_client.close())
                        loop.close()
                except Exception as e:
                    print(f"关闭server5_async_client时出错: {e}")

            # 2. 停止传感器定时器
            if hasattr(self, 'sensor_timer'):
                self.sensor_timer.stop()

            # 3. 停止WebSocket连接
            if hasattr(self, 'websocket_task') and self.websocket_task:
                self.websocket_task.cancel()

            # 4. 停止异步任务
            if hasattr(self, 'async_task') and self.async_task:
                self.async_task.cancel()

            # 5. 关闭通知WebSocket连接
            if hasattr(self, 'notification_connected') and self.notification_connected:
                self.log_employee_message("正在断开通知WebSocket连接...")
                self.notification_connected = False
                
                if hasattr(self, 'notification_websocket') and self.notification_websocket:
                    try:
                        # 在事件循环中关闭WebSocket
                        if hasattr(self, 'app_loop') and self.app_loop and not self.app_loop.is_closed():
                            asyncio.run_coroutine_threadsafe(
                                self.notification_websocket.close(), 
                                self.app_loop
                            )
                        elif hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
                            asyncio.run_coroutine_threadsafe(
                                self.notification_websocket.close(), 
                                self.loop
                            )
                        else:
                            # 如果没有可用的事件循环，创建一个新的来关闭
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(self.notification_websocket.close())
                            loop.close()
                    except Exception as e:
                        print(f"关闭通知WebSocket时出错: {e}")
                    finally:
                        self.notification_websocket = None
                
                # 等待通知连接线程结束
                if hasattr(self, 'notification_connect_thread') and self.notification_connect_thread and self.notification_connect_thread.is_alive():
                    try:
                        self.notification_connect_thread.join(timeout=2.0)  # 等待最多2秒
                        if self.notification_connect_thread.is_alive():
                            print("通知连接线程未能在超时时间内结束")
                    except Exception as e:
                        print(f"等待通知连接线程结束时出错: {e}")

            # 6. 关闭事件循环
            if hasattr(self, 'loop') and self.loop and self.loop.is_running():
                self.loop.call_soon_threadsafe(self.loop.stop)

            self.log_employee_message("Program1窗口已关闭")
            print("Program1资源清理完成")

        except Exception as e:
            print(f"关闭窗口时发生错误: {e}")

        self.return_to_main()
        event.accept()

    def _schedule_async_task(self, coro):
        """调度异步任务，在PyQt6环境中安全运行"""
        try:
            # 在新线程中运行异步任务  20250708 + 16:00 + 相关主题: 修复闪退问题
            import threading
            
            def run_coro():
                try:
                    # 在新线程中创建新的事件循环
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # 运行协程
                        loop.run_until_complete(coro)
                    finally:
                        loop.close()
                except Exception as e:
                    # 使用QMetaObject.invokeMethod在主线程中记录错误
                    QtCore.QMetaObject.invokeMethod(
                        self, 
                        "log_employee_message", 
                        QtCore.Qt.ConnectionType.QueuedConnection,
                        QtCore.Q_ARG(str, f"❌ 异步任务执行失败: {e}")
                    )
            
            thread = threading.Thread(target=run_coro, daemon=True)
            thread.start()
            
        except Exception as e:
            self.log_employee_message(f"❌ 异步任务调度失败: {e}")


    def _populate_table(self, table, records):
        table.clearContents()
        table.setRowCount(0)
        if not records:
            return

        # 对Table3，强制用table3_headers顺序
        if table == self.table3:
            #20250710+17：30 - 在"日付"之后添加"曜日"列
            table3_headers = ['DB_ID', '従業員ｺｰﾄﾞ', '日付', '曜日', '機種', '号機', '工場製番', '工事番号', 'ﾕﾆｯﾄ番号', '区分', '項目', '時間', '所属ｺｰﾄﾞ']
            table.setColumnCount(len(table3_headers))
            table.setHorizontalHeaderLabels(table3_headers)
            table.setRowCount(len(records))
            
            #20250710+17：30 - 日语星期映射
            japanese_weekdays = ['月', '火', '水', '木', '金', '土', '日']
            
            for r, rec in enumerate(records):
                # 确保rec是字典类型
                if isinstance(rec, dict):
                    for c, k in enumerate(table3_headers):
                        if k == '曜日':
                            #20250710+17：30 - 根据"日付"计算曜日
                            date_str = rec.get('日付', '')
                            if date_str:
                                try:
                                    date_obj = datetime.strptime(str(date_str), '%Y-%m-%d')
                                    weekday_num = date_obj.weekday()  # 0=Monday, 6=Sunday
                                    japanese_weekday = japanese_weekdays[weekday_num]
                                    table.setItem(r, c, QtWidgets.QTableWidgetItem(japanese_weekday))
                                except ValueError:
                                    # 如果日期格式不正确，显示空
                                    table.setItem(r, c, QtWidgets.QTableWidgetItem(""))
                            else:
                                table.setItem(r, c, QtWidgets.QTableWidgetItem(""))
                        elif k == 'DB_ID':
                            #20250710+18：00 - DB_ID应该对应entries的external_id，而不是id
                            value = rec.get('external_id', '')
                            table.setItem(r, c, QtWidgets.QTableWidgetItem(str(value)))
                        else:
                            value = rec.get(k, "")
                            table.setItem(r, c, QtWidgets.QTableWidgetItem(str(value)))
                else:
                    # 如果不是字典，显示错误信息
                    table.setItem(r, 0, QtWidgets.QTableWidgetItem(f"数据格式错误: {str(rec)}"))
            table.resizeColumnsToContents()
            return

        # #20250711+16：40+修改的主题 - 移除对table1的特殊处理，因为它现在使用Model
        # table1现在使用QTableView + Table1Model，不再在这里处理

        # 其他表格，保持原逻辑
        if records and isinstance(records[0], dict):
            headers = list(records[0].keys())
            table.setColumnCount(len(headers))
            table.setHorizontalHeaderLabels(headers)
            table.setRowCount(len(records))
            for r, rec in enumerate(records):
                for c, k in enumerate(headers):
                    table.setItem(r, c, QtWidgets.QTableWidgetItem(str(rec.get(k, ""))))
            table.resizeColumnsToContents()
        else:
            # 如果数据格式不正确，显示错误信息
            table.setColumnCount(1)
            table.setHorizontalHeaderLabels(['错误'])
            table.setRowCount(len(records))
            for r, rec in enumerate(records):
                table.setItem(r, 0, QtWidgets.QTableWidgetItem(f"数据格式错误: {str(rec)}"))





    def log_employee_message(self, message):
        """记录员工操作日志"""
        timestamp = QtCore.QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.employee_log.append(f"[{timestamp}] {message}")
