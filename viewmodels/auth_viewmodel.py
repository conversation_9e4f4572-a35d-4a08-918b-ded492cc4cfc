from PySide6.QtCore import QObject, Signal, Slot
from typing import Optional, Dict, Any
import requests
import threading
from models.user import UserModel
from services.http_service import HTTPService
from services.hardware_service import HardwareFingerprintService
from services.password_service import PasswordManagerService, ConfigService

class AuthViewModel(QObject):
    """认证视图模型"""
    
    # 信号定义
    login_successful = Signal(UserModel)  # 登录成功信号
    login_failed = Signal(str)  # 登录失败信号
    register_successful = Signal(str)  # 注册成功信号
    register_failed = Signal(str)  # 注册失败信号
    delete_registration_successful = Signal(str)  # 删除注册成功信号
    delete_registration_failed = Signal(str)  # 删除注册失败信号
    hardware_verification_failed = Signal(str)  # 硬件验证失败信号
    
    def __init__(self, http_service: HTTPService):
        super().__init__()
        self.http_service = http_service
        self.hardware_service = HardwareFingerprintService()
        self.password_service = PasswordManagerService()
        self.config_service = ConfigService()
        
        # 当前用户状态
        self.current_user: Optional[UserModel] = None
    
    @Slot(str, str, bool)
    def login(self, employee_id: str, password: str, remember_password: bool = False):
        """登录处理"""
        if not employee_id or not password:
            self.login_failed.emit("请输入员工ID和密码")
            return
        
        try:
            # 先进行硬件指纹验证
            self._verify_hardware_fingerprint(employee_id, password)
            
        except Exception as e:
            self.login_failed.emit(f"登录异常: {e}")
    
    def _verify_hardware_fingerprint(self, employee_id: str, password: str):
        """硬件指纹验证"""
        try:
            # 收集硬件信息
            hardware_info = self.hardware_service.collect_all_dict()
            
            hardware_verify_data = {
                "employee_id": employee_id,
                "password": password,
                **hardware_info
            }
            
            # 发送硬件指纹验证请求
            resp = self.http_service.get_sync_client().post(
                "/auth/api/hardware/verify",
                timeout=10,
                json=hardware_verify_data,
                headers={"Content-Type": "application/json"}
            )
            
            # 检查硬件指纹验证结果
            if not resp.get('ok'):
                if resp.get('status_code') == 404:
                    error_msg = "该员工ID未注册硬件指纹，请先点击'注册'按钮进行注册"
                elif resp.get('status_code') == 401:
                    error_msg = "硬件指纹验证失败，请确认您使用的是注册时的设备"
                else:
                    error_msg = f"硬件指纹验证失败: {resp.get('data', '未知错误')}"
                
                self.hardware_verification_failed.emit(error_msg)
                return
            
            # 硬件验证成功，继续登录流程
            self._perform_login(employee_id, password)
            
        except Exception as e:
            self.login_failed.emit(f"硬件验证异常: {e}")
    
    def _perform_login(self, employee_id: str, password: str):
        """执行登录"""
        try:
            # 发送登录请求到认证服务
            login_data = {
                "employee_id": employee_id,
                "password": password
            }
            
            resp = self.http_service.get_sync_client().post(
                "/auth/login", 
                timeout=5, 
                json=login_data,
                headers={"Content-Type": "application/json"}
            )
            
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('success'):
                    # 登录成功
                    employee_name = data.get('employee_name', '')
                    token = data.get('access_token', '')
                    
                    # 创建用户模型
                    user = UserModel(
                        employee_id=employee_id,
                        employee_name=employee_name,
                        token=token
                    )
                    
                    self.current_user = user
                    
                    # 保存密码（如果需要）
                    if self.password_service.save_password(employee_id, password):
                        print(f"已保存员工ID '{employee_id}' 的密码")
                    
                    # 保存最近使用的员工ID
                    self.config_service.save_last_used_employee_id(employee_id)
                    
                    # 发射登录成功信号
                    self.login_successful.emit(user)
                    
                else:
                    # 登录失败
                    error_msg = data.get('message', '登录失败')
                    self.login_failed.emit(error_msg)
            else:
                error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
                self.login_failed.emit(error_msg)
                
        except Exception as e:
            self.login_failed.emit(f"登录异常: {e}")
    
    @Slot(str, str)
    def register_hardware_fingerprint(self, employee_id: str, password: str):
        """注册硬件指纹"""
        if not employee_id or not password:
            self.register_failed.emit("请输入员工ID和密码")
            return
        
        try:
            # 收集硬件信息
            hardware_info = self.hardware_service.collect_all_dict()
            
            # 发送注册请求
            registration_data = {
                "employee_id": employee_id,
                "password": password,
                **hardware_info
            }
            
            resp = self.http_service.get_sync_client().post(
                "/auth/api/hardware/register",
                timeout=10,
                json=registration_data,
                headers={"Content-Type": "application/json"}
            )
            
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    success_msg = f"硬件指纹注册成功！\n\n员工ID: {data.get('employee_id', '')}\n注册时间: {data.get('registration_time', '')}\n\n现在可以使用此设备进行登录。"
                    self.register_successful.emit(success_msg)
                else:
                    error_msg = data.get('message', '注册失败')
                    self.register_failed.emit(error_msg)
            else:
                error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
                self.register_failed.emit(error_msg)
                
        except Exception as e:
            self.register_failed.emit(f"注册异常: {e}")
    
    @Slot(str, str)
    def delete_hardware_registration(self, employee_id: str, admin_password: str):
        """删除硬件指纹注册"""
        if not employee_id or not admin_password:
            self.delete_registration_failed.emit("请输入员工ID和管理员密码")
            return
        
        try:
            delete_data = {
                "employee_id": employee_id,
                "admin_password": admin_password
            }
            
            resp = self.http_service.get_sync_client().post(
                "/auth/api/hardware/delete",
                timeout=10,
                json=delete_data,
                headers={"Content-Type": "application/json"}
            )
            
            if resp.get('ok'):
                data = resp.get('data', {})
                if data.get('status') == 'success':
                    success_msg = f"员工ID '{employee_id}' 的硬件指纹注册已成功删除！\n\n删除时间: {data.get('deletion_time', '')}\n\n该员工现在可以重新注册硬件指纹。"
                    self.delete_registration_successful.emit(success_msg)
                    
                    # 删除保存的密码
                    if self.password_service.remove_password(employee_id):
                        print(f"已删除员工ID '{employee_id}' 的保存密码")
                else:
                    error_msg = data.get('message', '删除失败')
                    self.delete_registration_failed.emit(error_msg)
            else:
                if resp.get('status_code') == 403:
                    error_msg = "管理员密码错误"
                elif resp.get('status_code') == 404:
                    error_msg = "该员工ID未注册硬件指纹"
                else:
                    error_msg = f"服务器错误: {resp.get('status_code')} {resp.get('data')}"
                
                self.delete_registration_failed.emit(error_msg)
                
        except Exception as e:
            self.delete_registration_failed.emit(f"删除异常: {e}")
    
    def load_saved_password(self, employee_id: str) -> str:
        """加载保存的密码"""
        return self.password_service.load_password(employee_id)
    
    def load_last_used_employee_id(self) -> Optional[str]:
        """加载最近使用的员工ID"""
        return self.config_service.load_last_used_employee_id()
    
    def get_current_user(self) -> Optional[UserModel]:
        """获取当前用户"""
        return self.current_user
    
    def clear_current_user(self):
        """清除当前用户"""
        self.current_user = None
    
    def verify_token(self) -> bool:
        """验证当前token是否有效"""
        if not self.current_user or not self.current_user.token:
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.current_user.token}"}
            auth_service_url = "http://localhost:8006"
            
            session = requests.Session()
            session.trust_env = False
            
            resp = session.get(
                f"{auth_service_url}/api/verify", 
                headers=headers, 
                timeout=5, 
                verify=False
            )
            
            return resp.status_code == 200
                
        except Exception as e:
            print(f"Token验证异常: {e}")
            return False 