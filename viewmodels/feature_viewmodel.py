from PySide6.QtCore import QObject, Signal, Slot
from typing import Dict, List, Optional
import requests
import subprocess
import sys
import platform
from pathlib import Path
from models.user import UserModel
from models.feature_flags import FeatureFlagsState
from services.http_service import HTTPService

class FeatureViewModel(QObject):
    """功能管理视图模型"""
    
    # 信号定义
    feature_flags_updated = Signal(dict)  # 功能开关更新信号
    program_launch_successful = Signal(str, int)  # 程序启动成功信号
    program_launch_failed = Signal(str, str)  # 程序启动失败信号
    permissions_updated = Signal(dict)  # 权限更新信号
    server_status_updated = Signal(bool, str)  # 服务器状态更新信号
    websocket_status_updated = Signal(bool)  # WebSocket状态更新信号
    
    def __init__(self, http_service: HTTPService):
        super().__init__()
        self.http_service = http_service
        self.feature_flags_state = FeatureFlagsState(flags={})
        self.running_programs: Dict[str, subprocess.Popen] = {}
        self.current_user: Optional[UserModel] = None
    
    def set_current_user(self, user: UserModel):
        """设置当前用户"""
        self.current_user = user
    
    def clear_current_user(self):
        """清除当前用户"""
        self.current_user = None
        self.running_programs.clear()
    
    @Slot()
    def refresh_server_status(self):
        """刷新服务器状态"""
        try:
            # 使用同步客户端进行快速状态检查
            resp = self.http_service.get_sync_client().get("/", timeout=3)
            if resp.get('ok'):
                self.server_status_updated.emit(True, "服务器: 已连接")
                
                # 检查数据库状态
                db_resp = self.http_service.get_sync_client().get("/api/db/status", timeout=3)
                if db_resp.get('ok'):
                    db_data = db_resp.get('data', {})
                    if db_data.get('connected'):
                        self.server_status_updated.emit(True, "数据库: 已连接")
                    else:
                        self.server_status_updated.emit(False, "数据库: 未连接")
                else:
                    self.server_status_updated.emit(False, "数据库: 状态异常")
            else:
                self.server_status_updated.emit(False, f"服务器: 错误 {resp.get('status_code', 'Unknown')}")
        except Exception as e:
            self.server_status_updated.emit(False, f"服务器: 未连接 - {e}")
    
    @Slot()
    def refresh_feature_flags(self):
        """刷新功能开关"""
        try:
            resp = self.http_service.get_sync_client().get("/api/feature_flags/", timeout=5)
            if resp.get('ok'):
                flags = resp.get('data', {})
                self.feature_flags_state.flags.update(flags)
                self.feature_flags_updated.emit(flags)
            else:
                print(f"Failed to fetch flags: {resp.get('status_code')}")
        except Exception as e:
            print(f"Error fetching feature flags: {e}")
    
    def update_feature_flags(self, flags: dict):
        """更新功能开关"""
        self.feature_flags_state.flags.update(flags)
        self.feature_flags_updated.emit(flags)
    
    def get_feature_flags(self) -> dict:
        """获取功能开关状态"""
        return self.feature_flags_state.flags
    
    @Slot(str)
    def toggle_feature_flag(self, flag_name: str):
        """切换功能开关"""
        current_state = self.feature_flags_state.get_flag(flag_name)
        self.feature_flags_state.set_flag(flag_name, not current_state)
        self.feature_flags_updated.emit({flag_name: not current_state})
    
    @Slot()
    def load_user_permissions(self):
        """加载用户权限"""
        if not self.current_user or not self.current_user.token:
            self.permissions_updated.emit({})
            return
        
        try:
            headers = {"Authorization": f"Bearer {self.current_user.token}"}
            
            resp = self.http_service.get_sync_client().get(
                f"http://localhost:8006/api/program/permissions/{self.current_user.employee_id}",
                timeout=5,
                headers=headers
            )
            
            if resp.get('ok'):
                permission_info = resp.get('data', {})
                self.current_user.permissions = permission_info.get('accessible_programs', [])
                self.permissions_updated.emit(permission_info)
            else:
                self.permissions_updated.emit({})
                
        except Exception as e:
            print(f"获取权限信息异常: {e}")
            self.permissions_updated.emit({})
    
    @Slot(str)
    def request_program_authorization(self, program_name: str) -> bool:
        """请求程序启动授权"""
        if not self.current_user or not self.current_user.token:
            self.program_launch_failed.emit(program_name, "用户未登录")
            return False
        
        try:
            headers = {
                "Authorization": f"Bearer {self.current_user.token}",
                "Content-Type": "application/json"
            }
            
            request_data = {
                "program_name": program_name,
                "employee_id": self.current_user.employee_id
            }
            
            # 使用认证服务客户端
            resp = self.http_service.get_sync_client_auth().post(
                "/api/program/authorize",
                timeout=5,
                headers=headers,
                json=request_data
            )
            
            if resp.get('ok'):
                result = resp.get('data', {})
                if result.get("permission_granted"):
                    return True
                else:
                    error_msg = result.get('message', '权限不足')
                    self.program_launch_failed.emit(program_name, error_msg)
                    return False
            else:
                self.program_launch_failed.emit(program_name, f"授权请求失败: HTTP {resp.get('status_code')}")
                return False
                
        except Exception as e:
            self.program_launch_failed.emit(program_name, f"授权请求异常: {e}")
            return False
    
    @Slot(str)
    def launch_program(self, program_name: str):
        """启动程序"""
        if not self.current_user or not self.current_user.token:
            self.program_launch_failed.emit(program_name, "请先登录后再启动程序")
            return
        
        # 验证token是否仍然有效
        if not self._verify_current_token():
            self.program_launch_failed.emit(program_name, "登录状态已过期，请重新登录")
            return
        
        # 请求程序授权
        if not self.request_program_authorization(program_name):
            return  # 权限验证失败，已在函数内显示错误信息
        
        try:
            # 获取程序路径 - 修复路径指向上级目录
            current_dir = Path(__file__).parent.parent.parent  # 指向client目录
            mvvm_program_path = None
            legacy_program_path = None
            
            if program_name == "program1":
                # 优先使用分离后的program1fold架构
                separated_program_path = current_dir / "program1fold" / "main.py"
                mvvm_program_path = current_dir / "program1_mvvm" / "app.py"
                legacy_program_path = current_dir / "program1.py"
                
                if separated_program_path.exists():
                    program_path = separated_program_path
                    program_display_name = "员工操作界面 (分离版)"
                    # 使用分离架构启动参数
                    args = [
                        sys.executable,  # Python解释器
                        str(program_path),
                        self.current_user.token,
                        self.current_user.employee_id,
                        self.current_user.employee_name
                    ]
                elif mvvm_program_path.exists():
                    program_path = mvvm_program_path
                    program_display_name = "员工操作界面 (MVVM版)"
                    # 使用MVVM架构启动参数，传递用户信息（使用命名参数）
                    args = [
                        sys.executable,  # Python解释器
                        str(program_path),
                        "--token", self.current_user.token,
                        "--employee-id", self.current_user.employee_id,
                        "--employee-name", self.current_user.employee_name
                    ]
                elif legacy_program_path.exists():
                    program_path = legacy_program_path
                    program_display_name = "员工操作界面 (传统版)"
                    # 使用传统架构启动参数
                    args = [
                        sys.executable,  # Python解释器
                        str(program_path),
                        self.current_user.token,
                        self.current_user.employee_id,
                        self.current_user.employee_name
                    ]
                else:
                    self.program_launch_failed.emit(program_name, f"未找到program1文件")
                    return
                    
            elif program_name == "program2":
                program_path = current_dir / "program2.py"
                program_display_name = "PLC编程工具"
                # 使用传统架构启动参数
                args = [
                    sys.executable,  # Python解释器
                    str(program_path),
                    self.current_user.token,
                    self.current_user.employee_id,
                    self.current_user.employee_name
                ]
            else:
                self.program_launch_failed.emit(program_name, f"未知程序: {program_name}")
                return
            
            if not program_path.exists():
                self.program_launch_failed.emit(program_name, f"未找到程序文件: {program_path}")
                return
            
            # 启动独立程序 - 使用程序文件所在目录作为工作目录
            if program_name == "program1":
                if separated_program_path and separated_program_path.exists():
                    # 分离架构使用program1fold目录作为工作目录
                    working_dir = current_dir / "program1fold"
                elif mvvm_program_path and mvvm_program_path.exists():
                    # MVVM架构使用program1_mvvm目录作为工作目录
                    working_dir = current_dir / "program1_mvvm"
                else:
                    # 传统架构使用client目录作为工作目录
                    working_dir = current_dir
            else:
                # 其他程序使用client目录作为工作目录
                working_dir = current_dir
                
            process = subprocess.Popen(
                args, 
                cwd=str(working_dir),  # 使用相应的工作目录
                creationflags=subprocess.CREATE_NEW_CONSOLE if platform.system() == "Windows" else 0
            )
            
            # 记录正在运行的程序
            self.running_programs[program_name] = process
            
            self.program_launch_successful.emit(program_display_name, process.pid)
            
        except Exception as e:
            self.program_launch_failed.emit(program_name, f"启动程序失败: {e}")
    
    def _verify_current_token(self) -> bool:
        """验证当前token是否有效"""
        if not self.current_user or not self.current_user.token:
            return False
        
        try:
            headers = {"Authorization": f"Bearer {self.current_user.token}"}
            
            # 使用认证服务客户端
            resp = self.http_service.get_sync_client_auth().get(
                "/api/verify", 
                headers=headers, 
                timeout=5
            )
            
            return resp.get('ok') and resp.get('status_code') == 200
                
        except Exception as e:
            print(f"Token验证异常: {e}")
            return False
    
    def get_running_programs(self) -> Dict[str, subprocess.Popen]:
        """获取正在运行的程序"""
        return self.running_programs.copy()
    
    def is_program_running(self, program_name: str) -> bool:
        """检查程序是否正在运行"""
        if program_name not in self.running_programs:
            return False
        
        process = self.running_programs[program_name]
        return process.poll() is None  # None表示进程仍在运行
    
    def stop_program(self, program_name: str):
        """停止程序"""
        if program_name in self.running_programs:
            process = self.running_programs[program_name]
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            finally:
                del self.running_programs[program_name]
    
    def stop_all_programs(self):
        """停止所有程序"""
        for program_name in list(self.running_programs.keys()):
            self.stop_program(program_name) 