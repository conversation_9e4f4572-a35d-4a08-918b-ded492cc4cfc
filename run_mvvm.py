#!/usr/bin/env python3
"""
MySuite Client MVVM版本启动脚本
"""

import sys
import os
from pathlib import Path

# 获取当前脚本所在的目录 (launcher)
current_dir = Path(__file__).parent
# 获取client目录
client_dir = current_dir.parent

# 将client目录添加到Python路径的最前面
if str(client_dir) not in sys.path:
    sys.path.insert(0, str(client_dir))

# 导入并运行MVVM版本
from launcher.main_mvvm import ClientApp

if __name__ == "__main__":
    app = ClientApp(sys.argv)
    sys.exit(app.exec()) 

